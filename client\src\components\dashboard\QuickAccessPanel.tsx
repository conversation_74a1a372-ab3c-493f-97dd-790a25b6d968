import { Link } from "wouter";
import { 
  Plus, 
  FileText, 
  CreditCard, 
  Package, 
  BarChart3 
} from "lucide-react";

const QuickAccessPanel = () => {
  const quickActions = [
    {
      name: "New Vehicle Check-In",
      icon: Plus,
      href: "/gate-pass/new",
    },
    {
      name: "Create New Job Card",
      icon: FileText,
      href: "/job-cards/new",
    },
    {
      name: "Process Payment",
      icon: CreditCard,
      href: "/billing",
    },
    // {
    //   name: "Parts Inventory",
    //   icon: Package,
    //   href: "/parts",
    // },
    // {
    //   name: "Generate Reports",
    //   icon: BarChart3,
    //   href: "/reports",
    // },
  ];
  
  return (
    <div className="bg-white shadow-sm rounded-lg">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
        <h3 className="text-lg font-medium leading-6 text-gray-900">Quick Actions</h3>
      </div>
      <div className="p-4">
        <div className="space-y-3">
          {quickActions.map((action, index) => (
            <Link key={index} href={action.href}>
              <a className="block px-4 py-3 bg-gray-50 hover:bg-primary/5 rounded-md flex items-center transition-colors">
                <action.icon className="h-5 w-5 mr-3 text-primary" />
                <span>{action.name}</span>
              </a>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default QuickAccessPanel;
