import { useQuery } from "@tanstack/react-query";
import { formatDate, getStatusColor, formatStatusLabel, getInitials } from "@/lib/utils";
import { Link } from "wouter";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";

interface Vehicle {
  id: number;
  make: string;
  model: string;
  registrationNumber: string;
}

interface Customer {
  id: number;
  name: string;
}

interface Technician {
  id: number;
  name: string;
  specialization: string;
}

interface JobCard {
  id: number;
  jobNumber: string;
  vehicleId: number;
  customerId: number;
  description: string;
  status: string;
  estimatedCompletionDate: string;
  technicianId: number;
  completionPercentage?: number;
  currentTask?: string;
  isWarrantyClaim?: boolean;
  isInsuranceClaim?: boolean;
  insuranceApproved?: boolean;
}

const ServiceJobsTable = () => {
  const { data: jobCards, isLoading: jobsLoading } = useQuery<JobCard[]>({
    queryKey: ["/api/jobcards"],
  });

  const { data: vehicles, isLoading: vehiclesLoading } = useQuery<Vehicle[]>({
    queryKey: ["/api/vehicles"],
  });

  const { data: customers, isLoading: customersLoading } = useQuery<Customer[]>({
    queryKey: ["/api/customers"],
  });

  const { data: technicians, isLoading: techniciansLoading } = useQuery<Technician[]>({
    queryKey: ["/api/technicians"],
  });

  const isLoading = jobsLoading || vehiclesLoading || customersLoading || techniciansLoading;

  if (isLoading) {
    return (
      <div className="shadow-sm overflow-hidden border-b border-gray-200 rounded-lg">
        <div className="flex justify-between items-center mb-4">
          <Skeleton className="h-8 w-48" />
          <div className="flex space-x-2">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
        <div className="min-w-full divide-y divide-gray-200 bg-white">
          <div className="bg-gray-50">
            <div className="grid grid-cols-7 gap-3 px-6 py-3">
              {[...Array(7)].map((_, i) => (
                <Skeleton key={i} className="h-4 w-full" />
              ))}
            </div>
          </div>
          <div className="divide-y divide-gray-200">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="px-6 py-4">
                <div className="grid grid-cols-7 gap-3">
                  {[...Array(7)].map((_, j) => (
                    <Skeleton key={j} className="h-6 w-full" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Helper functions to get related data
  const getVehicle = (vehicleId: number) => vehicles?.find(v => v.id === vehicleId);
  const getCustomer = (customerId: number) => customers?.find(c => c.id === customerId);
  const getTechnician = (technicianId: number) => technicians?.find(t => t.id === technicianId);
  const getMakeInitials = (make: string) => make?.substring(0, 2).toUpperCase() || '';

  return (
    <div className="mt-8 flex flex-col">
      <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
          {/* Command Bar */}
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-900">Active Service Jobs</h2>
            <div className="flex space-x-2">
              <Select defaultValue="ALL">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Jobs" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Jobs</SelectItem>
                  <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                  <SelectItem value="PENDING_APPROVAL">Pending Approval</SelectItem>
                  <SelectItem value="READY_FOR_PICKUP">Ready for Pickup</SelectItem>
                </SelectContent>
              </Select>
              <Link href="/job-cards">
                <Button className="inline-flex items-center text-white bg-primary hover:bg-primary/90 cursor-not-allowed" disabled={true}>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  New Job
                </Button>
              </Link>
            </div>
          </div>
          
          {/* Jobs Table */}
          <div className="shadow-sm overflow-hidden border-b border-gray-200 rounded-lg">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Vehicle / Customer
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Service Details
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Technician
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Completion
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Est. Completion
                  </th>
                  <th scope="col" className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {jobCards?.length ? (
                  jobCards.map((job) => {
                    const vehicle = getVehicle(job.vehicleId);
                    const customer = getCustomer(job.customerId);
                    const technician = getTechnician(job.technicianId);
                    const makeInitials = vehicle ? getMakeInitials(vehicle.make) : '';
                    
                    let makeColorClass = "bg-primary/10 text-primary";
                    if (vehicle?.make === "Toyota") makeColorClass = "bg-amber-500/10 text-amber-500";
                    else if (vehicle?.make === "Ford") makeColorClass = "bg-green-500/10 text-green-500";
                    
                    return (
                      <tr key={job.id} className="hover:bg-gray-50 cursor-pointer">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className={cn("flex-shrink-0 h-10 w-10 rounded flex items-center justify-center font-bold", makeColorClass)}>
                              <span>{makeInitials}</span>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{vehicle?.make} {vehicle?.model}</div>
                              <div className="text-sm text-gray-500">{vehicle?.registrationNumber}</div>
                              <div className="text-sm text-gray-500">{customer?.name}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm text-gray-900">{job.description}</div>
                            <div className="text-sm text-gray-500">{job.jobNumber}</div>
                            {job.isWarrantyClaim && (
                              <div className="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-600 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span className="text-xs text-green-600">Warranty Covered</span>
                              </div>
                            )}
                            {job.isInsuranceClaim && !job.insuranceApproved && (
                              <div className="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-amber-500 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                                <span className="text-xs text-amber-500">Insurance: Pending Approval</span>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {technician ? (
                            <div className="text-sm text-gray-900">{technician.name}</div>
                          ) : (
                            <div className="text-sm text-gray-500">Not assigned</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex flex-col">
                            <span className={cn("px-2 inline-flex text-xs leading-5 font-semibold rounded-full", getStatusColor(job.status))}>
                              {formatStatusLabel(job.status)}
                            </span>
                            {job.currentTask && (
                              <span className="mt-1 text-xs text-gray-500">
                                Working on: {job.currentTask}
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {job.status === "COMPLETED" ? (
                            <div className="text-sm font-medium text-green-600">100%</div>
                          ) : job.status === "READY_FOR_PICKUP" ? (
                            <div className="text-sm font-medium text-blue-600">100%</div>
                          ) : job.completionPercentage ? (
                            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                              <div 
                                className="bg-primary h-2.5 rounded-full" 
                                style={{ width: `${job.completionPercentage}%` }}
                              ></div>
                              <div className="text-xs mt-1">{job.completionPercentage}% complete</div>
                            </div>
                          ) : (
                            <div className="text-sm text-gray-500">Not started</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {job.status === "READY_FOR_PICKUP" || job.status === "COMPLETED" 
                            ? "Completed" 
                            : job.estimatedCompletionDate 
                              ? formatDate(job.estimatedCompletionDate) 
                              : "Not scheduled"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link href={`/job-cards/${job.id}`}>
                            <a className="text-primary hover:text-primary/80">View</a>
                          </Link>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                      No active jobs found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceJobsTable;
