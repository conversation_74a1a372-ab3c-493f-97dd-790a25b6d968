import { pgTable, text, serial, integer, boolean, timestamp, jsonb, doublePrecision } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Customer schema
export const customers = pgTable("customers", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  phone: text("phone").notNull(),
  address: text("address"),
});

export const insertCustomerSchema = createInsertSchema(customers).omit({ id: true });
export type InsertCustomer = z.infer<typeof insertCustomerSchema>;
export type Customer = typeof customers.$inferSelect;

// Vehicle schema
export const vehicles = pgTable("vehicles", {
  id: serial("id").primaryKey(),
  customerId: integer("customer_id").notNull(),
  make: text("make").notNull(),
  model: text("model").notNull(),
  year: integer("year").notNull(),
  registrationNumber: text("registration_number").notNull().unique(),
  vin: text("vin").notNull().unique(),
  color: text("color"),
  insuranceProvider: text("insurance_provider"),
  insurancePolicy: text("insurance_policy"),
  warrantyInfo: text("warranty_info"),
});

export const insertVehicleSchema = createInsertSchema(vehicles).omit({ id: true });
export type InsertVehicle = z.infer<typeof insertVehicleSchema>;
export type Vehicle = typeof vehicles.$inferSelect;

// Technician schema
export const technicians = pgTable("technicians", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  specialization: text("specialization").notNull(),
  email: text("email").notNull(),
  phone: text("phone").notNull(),
  isAvailable: boolean("is_available").notNull().default(true),
});

export const insertTechnicianSchema = createInsertSchema(technicians).omit({ id: true });
export type InsertTechnician = z.infer<typeof insertTechnicianSchema>;
export type Technician = typeof technicians.$inferSelect;

// Service schema
export const services = pgTable("services", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  estimatedHours: doublePrecision("estimated_hours").notNull(),
  price: doublePrecision("price").notNull(),
  category: text("category").notNull(),
});

export const insertServiceSchema = createInsertSchema(services).omit({ id: true });
export type InsertService = z.infer<typeof insertServiceSchema>;
export type Service = typeof services.$inferSelect;

// Parts schema
export const parts = pgTable("parts", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  partNumber: text("part_number").notNull().unique(),
  description: text("description"),
  price: doublePrecision("price").notNull(),
  quantity: integer("quantity").notNull().default(0),
  category: text("category").notNull(),
});

export const insertPartSchema = createInsertSchema(parts).omit({ id: true });
export type InsertPart = z.infer<typeof insertPartSchema>;
export type Part = typeof parts.$inferSelect;

// JobStatus enum
export const JobStatusEnum = z.enum([
  "PENDING",
  "IN_PROGRESS",
  "PENDING_APPROVAL",
  "PARTS_WAITING",
  "QUALITY_CHECK",
  "READY_FOR_PICKUP",
  "COMPLETED",
  "CANCELLED",
]);

export type JobStatus = z.infer<typeof JobStatusEnum>;

// Job cards schema
export const jobCards = pgTable("job_cards", {
  id: serial("id").primaryKey(),
  jobNumber: text("job_number").notNull().unique(),
  vehicleId: integer("vehicle_id").notNull(),
  customerId: integer("customer_id").notNull(),
  description: text("description").notNull(),
  status: text("status").notNull().default("PENDING"),
  estimatedCompletionDate: timestamp("estimated_completion_date"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
  initialPhotos: jsonb("initial_photos").$type<string[]>(),
  finalPhotos: jsonb("final_photos").$type<string[]>(),
  technicianId: integer("technician_id"),
  estimatedCost: doublePrecision("estimated_cost"),
  actualCost: doublePrecision("actual_cost"),
  notes: text("notes"),
  isInsuranceClaim: boolean("is_insurance_claim").default(false),
  isWarrantyClaim: boolean("is_warranty_claim").default(false),
  insuranceApproved: boolean("insurance_approved"),
  qualityCheckPassed: boolean("quality_check_passed"),
  invoiceGenerated: boolean("invoice_generated").default(false),
  paymentReceived: boolean("payment_received").default(false),
  feedback: text("feedback"),
  feedbackRating: integer("feedback_rating"),
});

// Create base insert schema
const baseJobCardSchema = createInsertSchema(jobCards).omit({ 
  id: true, 
  jobNumber: true, 
  createdAt: true, 
  updatedAt: true,
  paymentReceived: true, 
  invoiceGenerated: true 
});

// Extend with custom validation for estimatedCompletionDate
export const insertJobCardSchema = baseJobCardSchema.extend({
  // Make vehicleId and customerId optional to allow for dynamic creation
  vehicleId: z.number().optional(),
  customerId: z.number().optional(),
  
  // Handle date fields properly
  estimatedCompletionDate: z.preprocess(
    // This preprocessor handles both Date objects and ISO strings
    (arg) => {
      if (arg instanceof Date) return arg;
      if (typeof arg === 'string') return new Date(arg);
      return undefined;
    },
    // This is the actual schema that validates the result
    z.date().optional().nullable()
  ),
  
  // Add custom fields from form
  customerName: z.string().optional(),
  makeModel: z.string().optional(),
  registrationNumber: z.string().optional(),
  chassisNumber: z.string().optional(),
  engineNumber: z.string().optional(),
  customerPhone: z.string().optional(),
  
  // Allow these fields to be included in updates
  paymentReceived: z.boolean().optional(),
  invoiceGenerated: z.boolean().optional()
});

export type InsertJobCard = z.infer<typeof insertJobCardSchema>;
export type JobCard = typeof jobCards.$inferSelect;

// Job service items schema
export const jobServiceItems = pgTable("job_service_items", {
  id: serial("id").primaryKey(),
  jobCardId: integer("job_card_id").notNull(),
  serviceId: integer("service_id").notNull(),
  quantity: integer("quantity").notNull().default(1),
  price: doublePrecision("price").notNull(),
  status: text("status").notNull().default("PENDING"),
  notes: text("notes"),
});

export const insertJobServiceItemSchema = createInsertSchema(jobServiceItems).omit({ id: true });
export type InsertJobServiceItem = z.infer<typeof insertJobServiceItemSchema>;
export type JobServiceItem = typeof jobServiceItems.$inferSelect;

// Job part items schema
export const jobPartItems = pgTable("job_part_items", {
  id: serial("id").primaryKey(),
  jobCardId: integer("job_card_id").notNull(),
  partId: integer("part_id").notNull(),
  quantity: integer("quantity").notNull().default(1),
  price: doublePrecision("price").notNull(),
  status: text("status").notNull().default("IN_STOCK"),
  notes: text("notes"),
});

export const insertJobPartItemSchema = createInsertSchema(jobPartItems).omit({ id: true });
export type InsertJobPartItem = z.infer<typeof insertJobPartItemSchema>;
export type JobPartItem = typeof jobPartItems.$inferSelect;

// Invoices schema
export const invoices = pgTable("invoices", {
  id: serial("id").primaryKey(),
  invoiceNumber: text("invoice_number").notNull().unique(),
  jobCardId: integer("job_card_id").notNull(),
  customerId: integer("customer_id").notNull(),
  subtotal: doublePrecision("subtotal").notNull(),
  tax: doublePrecision("tax").notNull(),
  total: doublePrecision("total").notNull(),
  status: text("status").notNull().default("UNPAID"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  paidAt: timestamp("paid_at"),
  paymentMethod: text("payment_method"),
  notes: text("notes"),
  isInsuranceClaim: boolean("is_insurance_claim").default(false),
  isWarrantyClaim: boolean("is_warranty_claim").default(false),
});

// Create base insert schema for invoices
const baseInvoiceSchema = createInsertSchema(invoices).omit({ 
  id: true, 
  invoiceNumber: true, 
  createdAt: true, 
  paidAt: true 
});

// Extend invoice schema with additional fields for updates
export const insertInvoiceSchema = baseInvoiceSchema.extend({
  paidAt: z.preprocess(
    (arg) => {
      if (arg instanceof Date) return arg;
      if (typeof arg === 'string') return new Date(arg);
      return undefined;
    },
    z.date().optional().nullable()
  )
});

export type InsertInvoice = z.infer<typeof insertInvoiceSchema>;
export type Invoice = typeof invoices.$inferSelect;

// Activity log schema
export const activityLogs = pgTable("activity_logs", {
  id: serial("id").primaryKey(),
  action: text("action").notNull(),
  entityType: text("entity_type").notNull(),
  entityId: integer("entity_id").notNull(),
  details: text("details"),
  timestamp: timestamp("timestamp").notNull().defaultNow(),
});

export const insertActivityLogSchema = createInsertSchema(activityLogs).omit({ 
  id: true, 
  timestamp: true 
});

export type InsertActivityLog = z.infer<typeof insertActivityLogSchema>;
export type ActivityLog = typeof activityLogs.$inferSelect;

// Users schema
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  name: text("name").notNull(),
  role: text("role").notNull(),
  email: text("email").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  name: true,
  role: true,
  email: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
