import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import FeedbackForm from "@/components/exit/FeedbackForm";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SearchIcon, Car, ClipboardCheck, CreditCard, CheckCircle2 } from "lucide-react";
import { formatDate, formatCurrency } from "@/lib/utils";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

const VehicleExit = () => {
  const [selectedJobId, setSelectedJobId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showExitDialog, setShowExitDialog] = useState(false);
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch all job cards
  const { data: jobCards, isLoading: jobsLoading } = useQuery({
    queryKey: ["/api/jobcards"],
  });

  // Fetch vehicles
  const { data: vehicles, isLoading: vehiclesLoading } = useQuery({
    queryKey: ["/api/vehicles"],
  });

  // Fetch customers
  const { data: customers, isLoading: customersLoading } = useQuery({
    queryKey: ["/api/customers"],
  });

  // Fetch invoices
  const { data: invoices, isLoading: invoicesLoading } = useQuery({
    queryKey: ["/api/invoices"],
  });

  // Update job card status mutation
  const updateJobStatusMutation = useMutation({
    mutationFn: async (data: { jobCardId: number; status: string }) => {
      const response = await apiRequest(
        "PATCH", 
        `/api/jobcards/${data.jobCardId}`, 
        { status: data.status }
      );
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/jobcards"] });
      toast({
        title: "Vehicle exit processed",
        description: "The vehicle has been successfully marked as exited",
      });
      setShowExitDialog(false);
      setShowFeedbackForm(true);
    },
    onError: (error) => {
      toast({
        title: "Error processing vehicle exit",
        description: "There was an error processing the vehicle exit. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Filter job cards that are ready for exit
  const exitReadyJobs = jobCards
    ? jobCards.filter((job: any) => {
        const matchesSearch =
          job.jobNumber.toLowerCase().includes(searchQuery.toLowerCase());
        
        // Jobs that are ready for exit (completed, payment received)
        const isExitReady = job.status === "READY_FOR_PICKUP" && job.qualityCheckPassed && job.paymentReceived;
        
        return matchesSearch && isExitReady;
      })
    : [];

  const getVehicleDetails = (vehicleId: number) => {
    return vehicles?.find((v: any) => v.id === vehicleId);
  };

  const getCustomerDetails = (customerId: number) => {
    return customers?.find((c: any) => c.id === customerId);
  };

  const getInvoiceDetails = (jobCardId: number) => {
    return invoices?.find((i: any) => i.jobCardId === jobCardId);
  };

  const handleExitVehicle = (jobCard: any) => {
    setSelectedJobId(jobCard.id);
    
    const vehicle = getVehicleDetails(jobCard.vehicleId);
    const customer = getCustomerDetails(jobCard.customerId);
    const invoice = getInvoiceDetails(jobCard.id);
    
    setSelectedVehicle({
      ...vehicle,
      jobCard,
      customer,
      invoice
    });
    
    setShowExitDialog(true);
  };

  const confirmExit = () => {
    if (selectedJobId) {
      updateJobStatusMutation.mutate({
        jobCardId: selectedJobId,
        status: "COMPLETED"
      });
    }
  };

  const handleFeedbackComplete = () => {
    setShowFeedbackForm(false);
    setSelectedJobId(null);
    setSelectedVehicle(null);
  };

  const isLoading = jobsLoading || vehiclesLoading || customersLoading || invoicesLoading;
  const isPending = updateJobStatusMutation.isPending;

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Vehicle Exit</h1>
        <p className="mt-1 text-gray-600">
          Process vehicle exits and collect customer feedback
        </p>

        <div className="mt-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div className="relative w-full md:w-96">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search job number..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Vehicles Ready for Exit</CardTitle>
              <CardDescription>
                Process the exit of vehicles with completed service and payment
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <p className="text-center py-4 text-gray-500">Loading data...</p>
              ) : exitReadyJobs.length === 0 ? (
                <div className="text-center py-8">
                  <Car className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No vehicles ready for exit</p>
                  <p className="text-sm text-gray-400 mt-1">
                    Vehicles appear here after service is completed and payment is received
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Job #</TableHead>
                        <TableHead>Vehicle</TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Payment</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {exitReadyJobs.map((job: any) => {
                        const vehicle = getVehicleDetails(job.vehicleId);
                        const customer = getCustomerDetails(job.customerId);
                        const invoice = getInvoiceDetails(job.id);
                        
                        return (
                          <TableRow key={job.id}>
                            <TableCell className="font-medium">{job.jobNumber}</TableCell>
                            <TableCell>
                              {vehicle ? (
                                <div>
                                  <div>{vehicle.make} {vehicle.model}</div>
                                  <div className="text-xs text-gray-500">{vehicle.registrationNumber}</div>
                                </div>
                              ) : (
                                "Vehicle ID: " + job.vehicleId
                              )}
                            </TableCell>
                            <TableCell>
                              {customer ? customer.name : "Customer ID: " + job.customerId}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <ClipboardCheck className="h-4 w-4 text-green-500 mr-1" />
                                <span className="text-sm">Ready for Pickup</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <CreditCard className="h-4 w-4 text-green-500 mr-1" />
                                <span className="text-sm">Paid</span>
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                onClick={() => handleExitVehicle(job)}
                              >
                                Process Exit
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {showFeedbackForm && selectedJobId && (
            <div className="mt-6">
              <FeedbackForm
                jobCardId={selectedJobId}
                onComplete={handleFeedbackComplete}
              />
            </div>
          )}
        </div>
      </div>

      {/* Vehicle Exit Dialog */}
      <Dialog open={showExitDialog} onOpenChange={setShowExitDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Confirm Vehicle Exit</DialogTitle>
            <DialogDescription>
              Verify details and confirm vehicle exit.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedVehicle && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Vehicle</h3>
                    <p className="mt-1 text-base font-medium">
                      {selectedVehicle.make} {selectedVehicle.model}
                    </p>
                    <p className="text-sm text-gray-600">
                      {selectedVehicle.registrationNumber}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Customer</h3>
                    <p className="mt-1 text-base font-medium">
                      {selectedVehicle.customer?.name || "Unknown"}
                    </p>
                  </div>
                </div>
                
                <div className="border-t border-b border-gray-200 py-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Service Summary</h3>
                  <p className="text-base">
                    {selectedVehicle.jobCard.description}
                  </p>
                  
                  <div className="mt-3 flex items-center gap-3">
                    <div className="flex items-center">
                      <CheckCircle2 className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-sm text-gray-600">Quality Check Passed</span>
                    </div>
                    <div className="flex items-center">
                      <CreditCard className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-sm text-gray-600">
                        Payment Received: {selectedVehicle.invoice ? formatCurrency(selectedVehicle.invoice.total) : "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-gray-600">
                    By confirming, you verify that the vehicle has been delivered to the customer
                    and all service items have been completed satisfactorily.
                  </p>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowExitDialog(false)}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button 
              onClick={confirmExit}
              disabled={isPending}
            >
              {isPending ? "Processing..." : "Confirm Exit"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default VehicleExit;
