ALTER TABLE "job_cards" DROP CONSTRAINT "job_cards_gate_pass_id_gate_pass_id_fk";
--> statement-breakpoint
ALTER TABLE "job_cards" DROP CONSTRAINT "job_cards_quotation_id_quotations_id_fk";
--> statement-breakpoint
ALTER TABLE "job_cards" DROP CONSTRAINT "job_cards_vehicle_id_vehicles_id_fk";
--> statement-breakpoint
ALTER TABLE "job_cards" DROP CONSTRAINT "job_cards_customer_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "job_cards" ALTER COLUMN "estimated_completion_date" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "job_number" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "vehicle_registration_number" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "vehicle_make" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "vehicle_model" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "vehicle_color" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "vehicle_engine_number" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "vehicle_chassis_number" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "customer_name" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "owner_name" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "customer_email" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "customer_phone" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "service_type" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "mileage" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "job_description" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "tax" text NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "total_amount" text;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "estimated_delivery_date" timestamp NOT NULL;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "remarks" text;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "quotation_notes" text;--> statement-breakpoint
ALTER TABLE "job_cards" ADD COLUMN "quotation_description" text;--> statement-breakpoint
ALTER TABLE "job_cards" DROP COLUMN "gate_pass_id";--> statement-breakpoint
ALTER TABLE "job_cards" DROP COLUMN "quotation_id";--> statement-breakpoint
ALTER TABLE "job_cards" DROP COLUMN "vehicle_id";--> statement-breakpoint
ALTER TABLE "job_cards" DROP COLUMN "customer_id";--> statement-breakpoint
ALTER TABLE "job_cards" ADD CONSTRAINT "job_cards_job_number_unique" UNIQUE("job_number");