import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  FileText,
  Activity,
  Clock,
  CreditCard,
  Home,
  BarChart3,
  Menu,
  X,
  Car,
  IdCard
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

type SidebarProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

const Sidebar = ({ open, setOpen }: SidebarProps) => {
  const [location] = useLocation();

  const navigation = [
    // { name: "Dashboard & Reports", href: "/", icon: LayoutDashboard },
    { name: "Job Card", href: "/job-card", icon: IdCard },
    { name: "Quotation", href: "/quotation", icon: Menu },
    { name: "Gate Pass", href: "/gate-pass", icon: FileText },
    // { name: "Service Tracking", href: "/service-tracking", icon: Activity },
    // { name: "Quality Check", href: "/quality-check", icon: Clock },
    // { name: "Billing & Payments", href: "/billing", icon: CreditCard },
    // { name: "Vehicle Exit", href: "/vehicle-exit", icon: Home },
  ];

  return (
    <>
      {/* Mobile sidebar overlay */}
      {open && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-30 md:hidden"
          onClick={() => setOpen(false)}
        />
      )}

      {/* Sidebar for mobile */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-white transform transition-transform ease-in-out duration-300 md:hidden",
          open ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex items-center justify-between h-16 px-4 bg-primary text-white">
          <h1 className="text-xl font-semibold">MRS Automaintenance</h1>
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-primary/80"
            onClick={() => setOpen(false)}
          >
            <X className="h-6 w-6" />
          </Button>
        </div>

        <div className="flex flex-col flex-grow overflow-y-auto">
          <nav className="flex-1 px-2 py-4 space-y-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setOpen(false)}
              >
                <a
                  className={cn(
                    "flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",
                    location === item.href
                      ? "bg-primary/10 text-primary border-l-4 border-primary"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                >
                  <item.icon
                    className={cn(
                      "mr-3 h-6 w-6",
                      location === item.href ? "text-primary" : "text-gray-500"
                    )}
                  />
                  {item.name}
                </a>
              </Link>
            ))}
          </nav>
        </div>

        <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
          <div className="flex-shrink-0 w-full">
            <div className="flex items-center">
              <div className="h-9 w-9 rounded-full bg-primary text-white flex items-center justify-center">
                <span className="text-sm font-medium">TD</span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">Tom Davis</p>
                <p className="text-xs text-gray-500">Service Advisor</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar for desktop */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64 border-r border-gray-200 bg-white">
          <div className="flex items-center h-16 px-4 bg-primary text-white">
            <h1 className="text-xl font-semibold">MRS Automaintenance</h1>
          </div>
          <div className="flex flex-col flex-grow overflow-y-auto">
            <nav className="flex-1 px-2 py-4 space-y-1">
              {navigation.map((item) => (
                <Link key={item.name} href={item.href} className={cn(
                      "flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",
                      location === item.href
                        ? "bg-primary/10 text-primary border-l-4 border-primary"
                        : "text-gray-700 hover:bg-gray-100"
                    )}>
                  {/* <a
                    className={cn(
                      "flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",
                      location === item.href
                        ? "bg-primary/10 text-primary border-l-4 border-primary"
                        : "text-gray-700 hover:bg-gray-100"
                    )}
                  > */}
                    <item.icon
                      className={cn(
                        "mr-3 h-6 w-6",
                        location === item.href ? "text-primary" : "text-gray-500"
                      )}
                    />
                    {item.name}
                  {/* </a> */}
                </Link>
              ))}
            </nav>
          </div>
          <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
            <div className="flex-shrink-0 w-full">
              <div className="flex items-center">
                <div className="h-9 w-9 rounded-full bg-primary text-white flex items-center justify-center">
                  <span className="text-sm font-medium">TD</span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">Tom Davis</p>
                  <p className="text-xs text-gray-500">Service Advisor</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;

