import { useState, useEffect } from "react";
import { useLocation, useRoute } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { fetchAPI } from "@/lib/api";
import { formatDate } from "@/lib/utils";
import { Loader2, ChevronLeft } from "lucide-react";

export default function JobCardFromQuotation() {
  const [, setLocation] = useLocation();
  const [match, params] = useRoute("/job-card-from-quotation/:id");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const quotationId = params?.id;

  // Fetch quotation details
  const { data: quotation, isLoading } = useQuery({
    queryKey: [`/api/quotations/${quotationId}`],
    queryFn: async () => {
      const response = await fetchAPI("GET", `/api/quotations/${quotationId}`);
      return response.json();
    },
    enabled: !!quotationId,
  });

  // Fetch gate pass details
  const { data: gatePass, isLoading: isLoadingGatePass } = useQuery({
    queryKey: [`/api/gate-pass/${quotation?.gatePassId}`],
    queryFn: async () => {
      const response = await fetchAPI("GET", `/api/gate-pass/${quotation?.gatePassId}`);
      return response.json();
    },
    enabled: !!quotation?.gatePassId,
  });

  // Create job card mutation
  const createJobCardMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetchAPI("POST", "/api/jobcards",  );
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["/api/jobcards"] });
      toast({
        title: "Job Card Created",
        description: "Job card has been created successfully from the quotation",
      });
      setLocation(`/job-card-details/${data.id}`);
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create job card from quotation",
        variant: "destructive",
      });
    },
  });

  const handleCreateJobCard = () => {
    if (!quotation || !gatePass) return;

    const jobCardData = {
      gatePassId: quotation.gatePassId,
      vehicleId: gatePass.vehicleId,
      customerId: gatePass.customerId || null, // Assuming customer ID is in gate pass
      description: gatePass.jobDescription,
      status: "IN_PROGRESS",
      estimatedCompletionDate: gatePass.estimatedDeliveryDate,
      partsUsed: quotation.partsUsed,
      servicesUsed: quotation.servicesUsed,
      quotationId: quotation.id,
      notes: quotation.notes || "",
    };

    createJobCardMutation.mutate(jobCardData);
  };

  if (isLoading || isLoadingGatePass) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <Button
        variant="outline"
        className="mb-4"
        onClick={() => setLocation("/quotations")}
      >
        <ChevronLeft className="mr-2 h-4 w-4" /> Back to Quotations
      </Button>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Create Job Card from Quotation #{quotation?.id}</CardTitle>
        </CardHeader>
        <CardContent>
          {quotation && gatePass ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium">Customer Details</h3>
                  <p>Name: {gatePass.customerName}</p>
                  <p>Phone: {gatePass.phone}</p>
                  <p>Email: {gatePass.email}</p>
                </div>
                <div>
                  <h3 className="font-medium">Vehicle Details</h3>
                  <p>Registration: {gatePass.vehicleRegistrationNumber}</p>
                  <p>Make/Model: {gatePass.make} {gatePass.model_model}</p>
                </div>
              </div>

              <div>
                <h3 className="font-medium">Job Description</h3>
                <p>{gatePass.jobDescription}</p>
              </div>

              <div>
                <h3 className="font-medium">Quotation Details</h3>
                <p>Created: {formatDate(quotation.createdAt)}</p>
                <p>Status: {quotation.status}</p>
              </div>

              <div className="pt-4">
                <Button 
                  onClick={handleCreateJobCard}
                  disabled={createJobCardMutation.isPending}
                >
                  {createJobCardMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Job Card"
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <p>Quotation or gate pass details not found</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

