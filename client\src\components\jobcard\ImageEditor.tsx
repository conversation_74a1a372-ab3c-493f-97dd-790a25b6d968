import React, { useRef, useEffect, useState } from "react";

import { fabric } from "fabric";

import pencil from "@/assets/pencil.png";

export default function ImageEditor({ imageUrl, onClose, onAttach }) {
  const canvasRef = useRef(null);

  const [canvas, setCanvas] = useState(null);

  useEffect(() => {
    const fabricCanvas = new fabric.Canvas(canvasRef.current, {
      isDrawingMode: true,
    });

    fabricCanvas.freeDrawingBrush.color = "#FF0000";

    fabricCanvas.freeDrawingBrush.width = 5;

    fabric.Image.fromURL(imageUrl, (img) => {
      fabricCanvas.setWidth(img.width);

      fabricCanvas.setHeight(img.height);

      fabricCanvas.setBackgroundImage(
        img,
        fabricCanvas.renderAll.bind(fabricCanvas)
      );
    });

    setCanvas(fabricCanvas);

    return () => {
      fabricCanvas.dispose();
    };
  }, [imageUrl]);

  const saveImage = () => {
    if (!canvas) return;

    const dataURL = canvas.toDataURL({
      format: "png",

      quality: 1,
    });

    const link = document.createElement("a");

    link.download = "scribbled.png";

    link.href = dataURL;

    link.click();
  };

  const saveCanvasState = () => {
    if (!canvas) return;

    const json = JSON.stringify(canvas.toJSON());

    console.log("Canvas JSON:", json);

    //   // Optional: download as a file

    //   const blob = new Blob([json], { type: "application/json" });

    //   const link = document.createElement("a");

    //   link.href = URL.createObjectURL(blob);

    //   link.download = "canvas_state.json";

    //   link.click();
  };

  const handleUndo = () => {
    if (!canvas) return;

    const objects = canvas.getObjects();

    if (objects.length > 0) {
      canvas.remove(objects[objects.length - 1]);

      canvas.renderAll();
    }
  };

  const handleAttach = () => {
    if (!canvas) return;

    const dataURL = canvas.toDataURL({
      format: "png",

      quality: 1,
    });

    if (onAttach) {
      const canvasJson = JSON.stringify(canvas.toJSON());
      onAttach(dataURL, canvasJson);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[9999]">
      {/* Top-right buttons */}

      <div className="absolute top-4 right-4 flex gap-2">
        <button
          onClick={handleUndo}
          className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 font-semibold"
        >
          Undo
        </button>

        <button
          onClick={saveCanvasState}
          className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 font-semibold"
        >
          {/* Save */}
          Export
        </button>

        <button
          onClick={handleAttach}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 font-semibold"
        >
          Attach
        </button>

        <button
          onClick={onClose}
          className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 font-semibold"
        >
          X
        </button>
      </div>

      {/* Canvas */}

      <canvas
        ref={canvasRef}
        className="border-2 border-white max-w-full max-h-[95%] !cursor-[url('@/assets/pencil.png'),_auto]"
      />
    </div>
  );
}
