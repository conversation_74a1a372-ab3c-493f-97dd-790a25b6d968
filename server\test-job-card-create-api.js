// Test script for the new job card creation API
const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3000/api/gate-pass';

async function testJobCardCreation() {
  console.log('🧪 Testing Job Card Creation API...\n');

  const testJobCardData = {
    // Customer information
    customerName: '<PERSON>',
    ownerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '+971501234567',
    customerAddress: '123 Test Street, Dubai, UAE',
    
    // Vehicle information
    vehicleRegistrationNumber: 'TEST-123',
    vehicleMake: 'Toyota',
    vehicleModel: 'Camry',
    vehicleColor: 'White',
    vehicleEngineNumber: 'ENG123456',
    vehicleChassisNumber: 'CHS789012',
    
    // Job details
    description: 'Oil change and brake inspection',
    jobDescription: 'Complete oil change and brake system inspection',
    remarks: 'Customer requested premium oil',
    
    // Parts and services
    partsUsed: [
      { id: 1, itemName: 'Engine Oil Filter', price: '25.00' },
      { id: 2, itemName: 'Brake Pads', price: '150.00' }
    ],
    servicesUsed: [
      { id: 1, itemName: 'Oil Change Service', price: '100.00' },
      { id: 2, itemName: 'Brake Inspection', price: '50.00' }
    ],
    
    // Financial details
    totalAmount: 325.00,
    tax: 16.25,
    
    // Default values
    status: 'pending',
    serviceType: 'Maintenance',
    mileage: '50000'
  };

  try {
    console.log('📤 Sending job card creation request...');
    console.log('Data:', JSON.stringify(testJobCardData, null, 2));

    const response = await fetch(`${API_BASE_URL}/job-card/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testJobCardData)
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error('❌ API Error:', responseData);
      return;
    }

    console.log('✅ Job Card Created Successfully!');
    console.log('📋 Response:', JSON.stringify(responseData, null, 2));

    // Verify the created job card
    if (responseData.jobCard) {
      const jobCard = responseData.jobCard;
      console.log('\n🔍 Verification:');
      console.log(`- Job Number: ${jobCard.jobNumber}`);
      console.log(`- Customer: ${jobCard.customerName}`);
      console.log(`- Vehicle: ${jobCard.vehicleMake} ${jobCard.vehicleModel}`);
      console.log(`- Registration: ${jobCard.vehicleRegistrationNumber}`);
      console.log(`- Total Amount: ${jobCard.totalAmount}`);
      console.log(`- Status: ${jobCard.status}`);
      console.log(`- Parts Count: ${Array.isArray(jobCard.partsUsed) ? jobCard.partsUsed.length : 'N/A'}`);
      console.log(`- Services Count: ${Array.isArray(jobCard.servicesUsed) ? jobCard.servicesUsed.length : 'N/A'}`);
    }

  } catch (error) {
    console.error('❌ Test Failed:', error.message);
  }
}

// Run the test
testJobCardCreation()
  .then(() => {
    console.log('\n🏁 Test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
