import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import ServiceAssignment from "@/components/service/ServiceAssignment";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SearchIcon, ClipboardList } from "lucide-react";
import { formatStatusLabel, getStatusColor } from "@/lib/utils";

const ServiceTracking = () => {
  const [selectedJobId, setSelectedJobId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch all job cards
  const { data: jobCards, isLoading } = useQuery({
    queryKey: ["/api/jobcards"],
  });

  // Filter job cards by status and search query
  const filteredJobCards = jobCards
    ? jobCards.filter((job: any) => {
        const matchesSearch =
          job.jobNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
          job.description.toLowerCase().includes(searchQuery.toLowerCase());
        
        return matchesSearch;
      })
    : [];

  // Active jobs are those not completed or cancelled
  const activeJobs = filteredJobCards.filter(
    (job: any) => job.status !== "COMPLETED" && job.status !== "CANCELLED"
  );

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Service Tracking</h1>
        <p className="mt-1 text-gray-600">
          Track and update the progress of all service jobs
        </p>

        <div className="mt-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div className="relative w-full md:w-96">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search job number or description..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Jobs List Panel */}
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle>Active Jobs</CardTitle>
                <CardDescription>
                  {activeJobs.length} jobs in progress
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <p className="text-center py-4 text-gray-500">Loading jobs...</p>
                ) : activeJobs.length === 0 ? (
                  <p className="text-center py-4 text-gray-500">No active jobs found</p>
                ) : (
                  <ul className="space-y-2">
                    {activeJobs.map((job: any) => (
                      <li key={job.id}>
                        <Button
                          variant="outline"
                          className={`w-full justify-start text-left h-auto py-4 ${
                            selectedJobId === job.id ? "border-primary" : ""
                          }`}
                          onClick={() => setSelectedJobId(job.id)}
                        >
                          <div className="flex flex-col items-start">
                            <div className="flex items-center">
                              <ClipboardList className="h-4 w-4 mr-2 text-gray-500" />
                              <span className="font-medium">{job.jobNumber}</span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1 line-clamp-1">
                              {job.description}
                            </p>
                            <div className={`px-2 py-1 text-xs rounded-full mt-2 ${getStatusColor(job.status)}`}>
                              {formatStatusLabel(job.status)}
                            </div>
                          </div>
                        </Button>
                      </li>
                    ))}
                  </ul>
                )}
              </CardContent>
            </Card>

            {/* Job Details Panel */}
            <div className="lg:col-span-2">
              {selectedJobId ? (
                <ServiceAssignment key={selectedJobId} jobCardId={selectedJobId} />
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>Service Tracking</CardTitle>
                    <CardDescription>
                      Select a job from the list to view and update its details
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <ClipboardList className="h-16 w-16 text-gray-300 mb-4" />
                    <p className="text-gray-500 text-center">
                      No job selected. Please select a job from the list to view its details.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceTracking;
