import { useQuery } from "@tanstack/react-query";
import { Clock, AlertTriangle, CheckCircle, Package } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

const ServiceStats = () => {
  const { data: jobCards, isLoading } = useQuery({
    queryKey: ["/api/jobcards"],
  });

  if (isLoading) {
    return (
      <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white overflow-hidden shadow-sm rounded-lg p-6">
            <Skeleton className="h-12 w-12 rounded-md mb-4" />
            <Skeleton className="h-4 w-24 mb-2" />
            <Skeleton className="h-8 w-16" />
          </div>
        ))}
      </div>
    );
  }

  // Count jobs by status
  const inProgress = jobCards?.filter((job: any) => job.status === "IN_PROGRESS")?.length || 0;
  const pendingApproval = jobCards?.filter((job: any) => job.status === "PENDING_APPROVAL")?.length || 0;
  const readyForPickup = jobCards?.filter((job: any) => job.status === "READY_FOR_PICKUP")?.length || 0;
  const newOrders = jobCards?.filter((job: any) => {
    const today = new Date();
    const jobDate = new Date(job.createdAt);
    return jobDate.toDateString() === today.toDateString();
  })?.length || 0;

  return (
    <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div className="bg-white overflow-hidden shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-primary/10 rounded-md p-3">
              <Clock className="h-6 w-6 text-primary" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dt className="text-sm font-medium text-gray-500 truncate">
                In Progress
              </dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">
                  {inProgress}
                </div>
                <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                  <span>On track</span>
                </div>
              </dd>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-amber-500/10 rounded-md p-3">
              <AlertTriangle className="h-6 w-6 text-amber-500" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dt className="text-sm font-medium text-gray-500 truncate">
                Pending Approval
              </dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">
                  {pendingApproval}
                </div>
                <div className="ml-2 flex items-baseline text-sm font-semibold text-amber-500">
                  <span>Insurance</span>
                </div>
              </dd>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-green-500/10 rounded-md p-3">
              <CheckCircle className="h-6 w-6 text-green-500" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dt className="text-sm font-medium text-gray-500 truncate">
                Ready for Pickup
              </dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">
                  {readyForPickup}
                </div>
                <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                  <span>Completed</span>
                </div>
              </dd>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-gray-100 rounded-md p-3">
              <Package className="h-6 w-6 text-gray-700" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dt className="text-sm font-medium text-gray-500 truncate">
                Service Orders Today
              </dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">
                  {newOrders}
                </div>
                <div className="ml-2 flex items-baseline text-sm font-semibold text-gray-500">
                  <span>New</span>
                </div>
              </dd>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceStats;
