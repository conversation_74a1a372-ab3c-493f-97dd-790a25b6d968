// src/index.ts
import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors'; // Add this import
import authRoutes from './routes/auth';
import gatePassRoutes from './routes/gate_pass';

// Load .env variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Enable CORS for all origins (you can restrict this in production)
app.use(cors());

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/', (_req, res) => {
  res.send('Garage ERP API is running 🚗');
});

// Routes
app.use('/api', authRoutes);
// Add other routes here, e.g.:

app.use('/api/gate-pass', gatePassRoutes);

// Global error handler (optional, but recommended)
app.use((err: any, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error(err);
  res.status(500).json({ error: 'Internal Server Error' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
