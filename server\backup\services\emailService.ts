import { MailService } from '@sendgrid/mail';

// Check for SendGrid API key
const sendgridApiKey = process.env.SENDGRID_API_KEY;

// Create a mail service instance
const mailService = new MailService();

// Set API key if available
if (sendgridApiKey) {
  mailService.setApiKey(sendgridApiKey);
}

// Define email parameters interface
export interface EmailParams {
  to: string;
  from: string;
  subject: string;
  text?: string;
  html?: string;
  attachments?: {
    content: string;
    filename: string;
    type: string;
    disposition: 'attachment' | 'inline';
  }[];
}

/**
 * Send an email using SendGrid
 * @param params Email parameters
 */
export async function sendEmail(params: EmailParams): Promise<boolean> {
  try {
    // Check if SendGrid API key is configured
    if (!sendgridApiKey) {
      console.error('SENDGRID_API_KEY environment variable is not set');
      return false;
    }

    await mailService.send({
      to: params.to,
      from: params.from || '<EMAIL>' as string,
      subject: params.subject,
      text: params.text,
      html: params.html,
      attachments: params.attachments,
    });
    
    return true;
  } catch (error) {
    console.error('SendGrid email error:', error);
    return false;
  }
}

/**
 * Generate HTML invoice content
 */
export function generateInvoiceHtml(invoice: any, jobCard: any, customer: any, vehicle: any, services: any[], parts: any[]): string {
  // Calculate totals
  const subtotal = [
    ...services.map((item: any) => item.price * item.quantity),
    ...parts.map((item: any) => item.price * item.quantity)
  ].reduce((sum, item) => sum + item, 0);
  
  const taxRate = 0.05; // 5% VAT
  const taxAmount = subtotal * taxRate;
  const total = subtotal + taxAmount;

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  // Format date
  const formatDate = (date: string | Date): string => {
    return new Date(date).toLocaleDateString('en-AE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Generate service items HTML
  const servicesHtml = services.map(service => `
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #e2e8f0;">${service.name}</td>
      <td style="padding: 8px; border-bottom: 1px solid #e2e8f0; text-align: right;">${service.quantity}</td>
      <td style="padding: 8px; border-bottom: 1px solid #e2e8f0; text-align: right;">${formatCurrency(service.price)}</td>
      <td style="padding: 8px; border-bottom: 1px solid #e2e8f0; text-align: right;">${formatCurrency(service.price * service.quantity)}</td>
    </tr>
  `).join('');

  // Generate part items HTML
  const partsHtml = parts.map(part => `
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #e2e8f0;">${part.name}</td>
      <td style="padding: 8px; border-bottom: 1px solid #e2e8f0; text-align: right;">${part.quantity}</td>
      <td style="padding: 8px; border-bottom: 1px solid #e2e8f0; text-align: right;">${formatCurrency(part.price)}</td>
      <td style="padding: 8px; border-bottom: 1px solid #e2e8f0; text-align: right;">${formatCurrency(part.price * part.quantity)}</td>
    </tr>
  `).join('');

  return `
  <!DOCTYPE html>
  <html>
  <head>
    <meta charset="utf-8">
    <title>Invoice #${invoice.invoiceNumber}</title>
    <style>
      body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
      .container { max-width: 800px; margin: 0 auto; padding: 20px; }
      .header { display: flex; justify-content: space-between; margin-bottom: 20px; }
      .logo { font-size: 24px; font-weight: bold; color: #1a56db; }
      .invoice-details { text-align: right; }
      .section { margin-bottom: 20px; }
      .section-title { font-weight: bold; margin-bottom: 10px; }
      table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
      th { background-color: #f8fafc; text-align: left; padding: 10px 8px; border-bottom: 2px solid #e2e8f0; }
      .text-right { text-align: right; }
      .totals { margin-left: auto; width: 300px; }
      .totals-row { display: flex; justify-content: space-between; padding: 8px 0; }
      .totals-row.total { font-weight: bold; font-size: 18px; border-top: 2px solid #e2e8f0; padding-top: 12px; }
      .notes { background-color: #f8fafc; padding: 15px; border-radius: 5px; }
      .footer { margin-top: 40px; text-align: center; font-size: 12px; color: #6b7280; }
      .claim-box { padding: 10px; margin-top: 15px; border-radius: 5px; }
      .insurance-claim { background-color: #e1effe; color: #1e429f; }
      .warranty-claim { background-color: #dcfce7; color: #166534; }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <div class="logo">MRS Automaintenance LLP</div>
        <div class="invoice-details">
          <h2>INVOICE</h2>
          <p><strong>Invoice #:</strong> ${invoice.invoiceNumber}</p>
          <p><strong>Date:</strong> ${formatDate(invoice.createdAt)}</p>
          <p><strong>Job Card:</strong> ${jobCard.jobNumber}</p>
        </div>
      </div>

      <div class="section">
        <div style="display: flex; justify-content: space-between;">
          <div style="width: 48%;">
            <div class="section-title">From:</div>
            <p>MRS Automaintenance LLP<br>
            123 Garage Street, Dubai<br>
            United Arab Emirates<br>
            Phone: +971 4 123 4567<br>
            Email: <EMAIL></p>
          </div>
          <div style="width: 48%;">
            <div class="section-title">Bill To:</div>
            <p>${customer.name}<br>
            ${customer.address || 'No address'}<br>
            Phone: ${customer.phone || 'No phone'}<br>
            Email: ${customer.email || 'No email'}</p>
            
            ${vehicle ? `<div class="section-title" style="margin-top: 15px;">Vehicle:</div>
            <p>${vehicle.make} ${vehicle.model} (${vehicle.registrationNumber})</p>` : ''}
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">Job Details:</div>
        <p>${jobCard.description}</p>
      </div>

      <div class="section">
        ${services.length > 0 ? `
        <div class="section-title">Services:</div>
        <table>
          <thead>
            <tr>
              <th>Description</th>
              <th class="text-right">Qty</th>
              <th class="text-right">Unit Price</th>
              <th class="text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            ${servicesHtml}
          </tbody>
        </table>
        ` : ''}

        ${parts.length > 0 ? `
        <div class="section-title">Parts:</div>
        <table>
          <thead>
            <tr>
              <th>Description</th>
              <th class="text-right">Qty</th>
              <th class="text-right">Unit Price</th>
              <th class="text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            ${partsHtml}
          </tbody>
        </table>
        ` : ''}
      </div>

      <div class="totals">
        <div class="totals-row">
          <span>Subtotal:</span>
          <span>${formatCurrency(subtotal)}</span>
        </div>
        <div class="totals-row">
          <span>Tax (5% VAT):</span>
          <span>${formatCurrency(taxAmount)}</span>
        </div>
        <div class="totals-row total">
          <span>Total:</span>
          <span>${formatCurrency(total)}</span>
        </div>
      </div>

      <div class="notes">
        <div class="section-title">Notes:</div>
        <p>Thank you for choosing MRS Automaintenance LLP for your vehicle service needs. Payment is due within 14 days from the invoice date.</p>
        
        ${invoice.isInsuranceClaim ? `
        <div class="claim-box insurance-claim">
          <strong>Insurance Claim</strong><br>
          This job is covered under an insurance claim.
        </div>
        ` : ''}
        
        ${invoice.isWarrantyClaim ? `
        <div class="claim-box warranty-claim">
          <strong>Warranty Claim</strong><br>
          This job is covered under warranty.
        </div>
        ` : ''}
      </div>

      <div class="footer">
        <p>If you have any questions about this invoice, please contact <NAME_EMAIL> or +971 4 123 4567</p>
      </div>
    </div>
  </body>
  </html>
  `;
}

/**
 * Generate WhatsApp message for invoice
 */
export function generateWhatsAppMessage(invoice: any, jobCard: any, customer: any, vehicle: any, services: any[], parts: any[]): string {
  // Calculate totals
  const subtotal = [
    ...services.map((item: any) => item.price * item.quantity),
    ...parts.map((item: any) => item.price * item.quantity)
  ].reduce((sum, item) => sum + item, 0);
  
  const taxRate = 0.05; // 5% VAT
  const taxAmount = subtotal * taxRate;
  const total = subtotal + taxAmount;

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  return `
*INVOICE #${invoice.invoiceNumber}*
MRS Automaintenance LLP

*Customer*: ${customer.name}
*Vehicle*: ${vehicle ? `${vehicle.make} ${vehicle.model} (${vehicle.registrationNumber})` : 'N/A'}
*Job Card*: ${jobCard.jobNumber}

*Total Amount Due*: ${formatCurrency(total)}

Please visit our website to view the full invoice and make a payment:
https://mrsauto.ae/invoice/${invoice.id}

Thank you for choosing MRS Automaintenance LLP for your vehicle service needs.
`;
}