// Test script for the job card creation API
// Run this with: node server/test-job-card-api.js
// Make sure the server is running on port 5000
// Requires Node.js 18+ for built-in fetch support

// Use built-in fetch (Node.js 18+) or fallback to a simple HTTP client
const fetch = globalThis.fetch || (async (url, options) => {
  const https = await import('https');
  const http = await import('http');
  const { URL } = await import('url');

  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const client = parsedUrl.protocol === 'https:' ? https : http;

    const req = client.request(url, {
      method: options?.method || 'GET',
      headers: options?.headers || {},
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });

    req.on('error', reject);

    if (options?.body) {
      req.write(options.body);
    }

    req.end();
  });
});

const API_BASE_URL = 'http://localhost:5000/api/gate-pass';

// Test data for creating a complete test scenario
const testData = {
  // Vehicle data
  vehicle: {
    vehicleRegistrationNumber: 'TEST-123',
    make: 'Toyota',
    model_model: 'Camry',
    color: 'White',
    engineNumber: 'ENG123456',
    chassisNumber: 'CHS789012'
  },
  
  // Gate pass data
  gatePass: {
    ownerName: 'John Doe',
    customerName: 'John Doe',
    email: '<EMAIL>',
    phone: '+971501234567',
    serviceType: 'Maintenance',
    mileage: '50000',
    estimatedDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    jobDescription: 'Oil change and brake inspection',
    remarks: 'Customer requested premium oil'
  },
  
  // Quotation data
  quotation: {
    partsUsed: [
      { itemName: 'Engine Oil Filter', price: '25.00' },
      { itemName: 'Brake Pads', price: '150.00' }
    ],
    servicesUsed: [
      { itemName: 'Oil Change Service', price: '75.00' },
      { itemName: 'Brake Inspection', price: '50.00' }
    ],
    tax: '5.0',
    notes: 'Premium service package',
    description: 'Complete maintenance service'
  }
};

// Helper function to make API calls
async function apiCall(endpoint, method = 'GET', body = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  if (body) {
    options.body = JSON.stringify(body);
  }
  
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, options);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} - ${JSON.stringify(data)}`);
    }
    
    return data;
  } catch (error) {
    console.error(`Error calling ${endpoint}:`, error.message);
    throw error;
  }
}

// Test functions
async function createTestVehicle() {
  console.log('🚗 Creating test vehicle...');
  
  const gatePassData = {
    vehicleId: 0, // Will create new vehicle
    vehicleNumber: testData.vehicle.vehicleRegistrationNumber,
    customerName: testData.gatePass.customerName,
    customerContact: testData.gatePass.phone,
    owner_name: testData.gatePass.ownerName,
    serviceType: testData.gatePass.serviceType,
    make_model: `${testData.vehicle.make} ${testData.vehicle.model_model}`,
    customer_email: testData.gatePass.email,
    mileage: testData.gatePass.mileage,
    status: 'pending',
    estimatedDeliveryDate: testData.gatePass.estimatedDeliveryDate,
    description: testData.gatePass.jobDescription,
    engine_number: testData.vehicle.engineNumber,
    chassis_number: testData.vehicle.chassisNumber,
    remarks: testData.gatePass.remarks
  };
  
  const result = await apiCall('/create', 'POST', gatePassData);
  console.log('✅ Gate pass created:', result.gatePass.id);
  return result.gatePass;
}

async function createTestQuotation(gatePassId) {
  console.log('📋 Creating test quotation...');
  
  const quotationData = {
    gatePassId,
    partsUsed: testData.quotation.partsUsed,
    servicesUsed: testData.quotation.servicesUsed,
    jobDescription: testData.quotation.description,
    remarks: testData.quotation.notes,
    tax: testData.quotation.tax
  };
  
  const result = await apiCall('/quotation/create', 'POST', quotationData);
  console.log('✅ Quotation created:', result.id);
  return result;
}

async function createJobCardFromQuotation(quotationId) {
  console.log('🔧 Creating job card from quotation...');
  
  const jobCardData = {
    quotationId
  };
  
  const result = await apiCall('/job-card/create-from-quotation', 'POST', jobCardData);
  console.log('✅ Job card created:', result.jobCard.id);
  return result;
}

async function verifyJobCardData(jobCard) {
  console.log('🔍 Verifying job card data...');
  
  const requiredFields = [
    'jobNumber',
    'vehicleRegistrationNumber',
    'vehicleMake',
    'vehicleModel',
    'vehicleColor',
    'vehicleEngineNumber',
    'vehicleChassisNumber',
    'customerName',
    'ownerName',
    'customerEmail',
    'customerPhone',
    'serviceType',
    'mileage',
    'description',
    'jobDescription',
    'partsUsed',
    'servicesUsed',
    'tax',
    'totalAmount',
    'status'
  ];
  
  const missingFields = requiredFields.filter(field => !jobCard[field]);
  
  if (missingFields.length > 0) {
    console.error('❌ Missing required fields:', missingFields);
    return false;
  }
  
  // Verify embedded data matches original test data
  const verifications = [
    { field: 'vehicleRegistrationNumber', expected: testData.vehicle.vehicleRegistrationNumber },
    { field: 'vehicleMake', expected: testData.vehicle.make },
    { field: 'customerName', expected: testData.gatePass.customerName },
    { field: 'customerEmail', expected: testData.gatePass.email },
    { field: 'customerPhone', expected: testData.gatePass.phone },
    { field: 'serviceType', expected: testData.gatePass.serviceType },
    { field: 'mileage', expected: testData.gatePass.mileage }
  ];
  
  let allVerified = true;
  for (const verification of verifications) {
    if (jobCard[verification.field] !== verification.expected) {
      console.error(`❌ Field ${verification.field}: expected "${verification.expected}", got "${jobCard[verification.field]}"`);
      allVerified = false;
    }
  }
  
  // Verify JSON fields are properly parsed
  try {
    const partsUsed = JSON.parse(jobCard.partsUsed);
    const servicesUsed = JSON.parse(jobCard.servicesUsed);
    
    if (!Array.isArray(partsUsed) || !Array.isArray(servicesUsed)) {
      console.error('❌ Parts or services data is not properly formatted as arrays');
      allVerified = false;
    }
    
    console.log('✅ Parts used:', partsUsed.length, 'items');
    console.log('✅ Services used:', servicesUsed.length, 'items');
  } catch (error) {
    console.error('❌ Error parsing parts/services JSON:', error.message);
    allVerified = false;
  }
  
  // Verify total amount calculation
  const totalAmount = parseFloat(jobCard.totalAmount);
  if (isNaN(totalAmount) || totalAmount <= 0) {
    console.error('❌ Total amount is not a valid positive number:', jobCard.totalAmount);
    allVerified = false;
  } else {
    console.log('✅ Total amount calculated:', jobCard.totalAmount);
  }
  
  if (allVerified) {
    console.log('✅ All job card data verified successfully!');
  }
  
  return allVerified;
}

// Main test function
async function runTests() {
  console.log('🧪 Starting Job Card API Tests...\n');
  
  try {
    // Step 1: Create test gate pass (which creates vehicle)
    const gatePass = await createTestVehicle();
    
    // Step 2: Create test quotation
    const quotation = await createTestQuotation(gatePass.id);
    
    // Step 3: Create job card from quotation
    const jobCardResult = await createJobCardFromQuotation(quotation.id);
    
    // Step 4: Verify job card data
    const isValid = await verifyJobCardData(jobCardResult.jobCard);
    
    console.log('\n📊 Test Summary:');
    console.log('- Gate Pass ID:', gatePass.id);
    console.log('- Quotation ID:', quotation.id);
    console.log('- Job Card ID:', jobCardResult.jobCard.id);
    console.log('- Job Number:', jobCardResult.jobCard.jobNumber);
    console.log('- Data Verification:', isValid ? '✅ PASSED' : '❌ FAILED');
    
    if (isValid) {
      console.log('\n🎉 All tests passed! The job card API is working correctly.');
    } else {
      console.log('\n❌ Some tests failed. Please check the API implementation.');
    }
    
  } catch (error) {
    console.error('\n💥 Test failed with error:', error.message);
    console.log('\nMake sure:');
    console.log('1. The server is running on http://localhost:5000');
    console.log('2. The database is connected and accessible');
    console.log('3. All required tables exist in the database');
  }
}

// Run the tests
runTests();
