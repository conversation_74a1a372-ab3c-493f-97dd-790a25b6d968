import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('ar-AE', {
    style: 'currency',
    currency: 'AED',
  }).format(amount);
}

export function formatDate(date: Date | string | null | undefined): string {
  if (!date) return 'N/A';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
  }).format(dateObj);
}

export function getInitials(name: string): string {
  if (!name) return '';
  
  return name
    .split(' ')
    .map(part => part.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
}

export function getStatusColor(status: string): string {
  const statusMap: Record<string, string> = {
    'IN_PROGRESS': 'bg-primary/10 text-primary',
    'PENDING_APPROVAL': 'bg-amber-500/10 text-amber-500',
    'READY_FOR_PICKUP': 'bg-green-500/10 text-green-500',
    'PENDING': 'bg-gray-500/10 text-gray-500',
    'PARTS_WAITING': 'bg-purple-500/10 text-purple-500',
    'QUALITY_CHECK': 'bg-blue-500/10 text-blue-500',
    'COMPLETED': 'bg-green-700/10 text-green-700',
    'CANCELLED': 'bg-red-500/10 text-red-500',
  };
  
  return statusMap[status] || 'bg-gray-500/10 text-gray-500';
}

export function formatStatusLabel(status: string): string {
  return status
    .split('_')
    .map(word => word.charAt(0) + word.slice(1).toLowerCase())
    .join(' ');
}

export function generateJobNumber(): string {
  const year = new Date().getFullYear();
  const randomPart = Math.floor(1000 + Math.random() * 9000);
  return `JB-${year}-${randomPart}`;
}

export function calculateTotalCost(services: any[], parts: any[]): number {
  const serviceTotal = services.reduce((sum, service) => sum + (service.price * service.quantity), 0);
  const partsTotal = parts.reduce((sum, part) => sum + (part.price * part.quantity), 0);
  return serviceTotal + partsTotal;
}

export const vehicleMakes = [
  "BMW", "Toyota", "Ford", "Honda", "Mercedes", "Audi", "Chevrolet", "Nissan", 
  "Volkswagen", "Hyundai", "Kia", "Volvo", "Subaru", "Mazda", "Lexus", "Jeep"
];
