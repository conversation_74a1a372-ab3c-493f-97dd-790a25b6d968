import React from 'react';

export const MRSLogo: React.FC = () => {
  return (
    <div className="flex flex-col items-center">
      <div className="flex items-center">
        <div className="w-10 h-10 rounded-full bg-blue-700 text-white flex items-center justify-center font-bold">M</div>
        <div className="w-10 h-10 rounded-full bg-red-600 text-white flex items-center justify-center font-bold mx-1">R</div>
        <div className="w-10 h-10 rounded-full bg-blue-700 text-white flex items-center justify-center font-bold">S</div>
      </div>
      <div className="text-blue-700 text-xs italic mt-1 text-center">Your Most Reliable Partner</div>
    </div>
  );
};

export const MRSLogoSmall: React.FC = () => {
  return (
    <div className="flex flex-col items-center">
      <div className="flex items-center">
        <div className="w-6 h-6 rounded-full bg-blue-700 text-white flex items-center justify-center font-bold text-xs">M</div>
        <div className="w-6 h-6 rounded-full bg-red-600 text-white flex items-center justify-center font-bold text-xs mx-1">R</div>
        <div className="w-6 h-6 rounded-full bg-blue-700 text-white flex items-center justify-center font-bold text-xs">S</div>
      </div>
      <div className="text-blue-700 text-[10px] italic mt-0.5 text-center">Your Most Reliable Partner</div>
    </div>
  );
};