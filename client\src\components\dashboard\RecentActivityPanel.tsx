import { useQuery } from "@tanstack/react-query";
import { Link } from "wouter";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDate } from "@/lib/utils";
import { 
  UserPlus, 
  CheckCircle, 
  AlertTriangle, 
  Package, 
  CreditCard 
} from "lucide-react";

interface ActivityLog {
  id: number;
  action: string;
  entityType: string;
  entityId: number;
  details: string;
  timestamp: string;
}

const getIconForActivity = (activity: ActivityLog) => {
  const { action, entityType } = activity;
  
  if (entityType === "CUSTOMER" && action === "CREATE") return UserPlus;
  if (entityType === "JOB_CARD" && action === "UPDATE" && activity.details.includes("completed")) return CheckCircle;
  if (entityType === "INVOICE" && action === "CREATE") return CreditCard;
  if (entityType === "INVOICE" && action === "UPDATE" && activity.details.includes("paid")) return CheckCircle;
  if (entityType === "JOB_CARD" && activity.details.includes("Insurance")) return AlertTriangle;
  if (entityType === "PART" || activity.details.includes("part")) return Package;
  
  // Default case
  return CheckCircle;
};

const getColorForActivity = (activity: ActivityLog) => {
  const { action, entityType } = activity;
  
  if (entityType === "CUSTOMER" && action === "CREATE") return "bg-primary/10 text-primary";
  if (entityType === "JOB_CARD" && action === "UPDATE" && activity.details.includes("completed")) return "bg-green-500/10 text-green-500";
  if (entityType === "INVOICE" && action === "CREATE") return "bg-primary/10 text-primary";
  if (entityType === "INVOICE" && action === "UPDATE" && activity.details.includes("paid")) return "bg-green-500/10 text-green-500";
  if (entityType === "JOB_CARD" && activity.details.includes("Insurance")) return "bg-amber-500/10 text-amber-500";
  if (entityType === "PART" || activity.details.includes("part")) return "bg-primary/10 text-primary";
  
  // Default case
  return "bg-gray-100 text-gray-700";
};

const RecentActivityPanel = () => {
  const { data: activityLogs, isLoading } = useQuery<ActivityLog[]>({
    queryKey: ["/api/activity"],
  });

  if (isLoading) {
    return (
      <div className="bg-white shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 flex justify-between items-center">
          <Skeleton className="h-6 w-36" />
          <Skeleton className="h-6 w-16" />
        </div>
        <div className="p-4">
          <ul className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <li key={i} className="p-3">
                <div className="flex">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="ml-4 w-full">
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-sm rounded-lg">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-medium leading-6 text-gray-900">Recent Activity</h3>
        <Link href="/activity">
          <a className="text-sm text-primary hover:text-primary/80">View All</a>
        </Link>
      </div>
      <div className="p-4">
        <ul className="space-y-4">
          {activityLogs && activityLogs.length > 0 ? (
            activityLogs.slice(0, 5).map((activity) => {
              const IconComponent = getIconForActivity(activity);
              const colorClass = getColorForActivity(activity);
              
              return (
                <li key={activity.id} className="p-3 hover:bg-gray-50 rounded-md transition-colors">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <div className={`h-10 w-10 rounded-full ${colorClass} flex items-center justify-center`}>
                        <IconComponent className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <p className="text-sm text-gray-900">{activity.details}</p>
                      <p className="text-xs text-gray-500 mt-1">{formatDate(activity.timestamp)}</p>
                    </div>
                  </div>
                </li>
              );
            })
          ) : (
            <li className="p-3 text-center text-gray-500">No recent activity</li>
          )}
        </ul>
      </div>
    </div>
  );
};

export default RecentActivityPanel;
