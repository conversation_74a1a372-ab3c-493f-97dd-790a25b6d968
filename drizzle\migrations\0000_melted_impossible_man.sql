CREATE TABLE "gate_pass" (
	"id" serial PRIMARY KEY NOT NULL,
	"vehicle_id" integer NOT NULL,
	"owner_name" text NOT NULL,
	"customer_name" text NOT NULL,
	"email" text NOT NULL,
	"phone" text NOT NULL,
	"service_type" text NOT NULL,
	"mileage" text NOT NULL,
	"estimated_delivery_date" timestamp NOT NULL,
	"job_description" text NOT NULL,
	"remarks" text,
	"status" text DEFAULT 'pending' NOT NULL,
	"canvas_json" json DEFAULT 'null'::json,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "quotations" (
	"id" serial PRIMARY KEY NOT NULL,
	"gate_pass_id" integer NOT NULL,
	"parts_used" json NOT NULL,
	"services_used" json NOT NULL,
	"tax" text NOT NULL,
	"notes" text,
	"description" text,
	"file_name" text,
	"status" text DEFAULT 'pending' NOT NULL,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "services" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_name" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"username" text NOT NULL,
	"password" text NOT NULL,
	"client" text NOT NULL,
	"created_at" timestamp DEFAULT now(),
	CONSTRAINT "users_username_unique" UNIQUE("username")
);
--> statement-breakpoint
CREATE TABLE "vehicle_parts" (
	"id" serial PRIMARY KEY NOT NULL,
	"item_name" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicles" (
	"id" serial PRIMARY KEY NOT NULL,
	"vehicle_registration_number" text NOT NULL,
	"make" text NOT NULL,
	"model_model" text NOT NULL,
	"color" text NOT NULL,
	"engine_number" text NOT NULL,
	"chassis_number" text NOT NULL,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "vehicles_vehicle_registration_number_unique" UNIQUE("vehicle_registration_number")
);
--> statement-breakpoint
ALTER TABLE "gate_pass" ADD CONSTRAINT "gate_pass_vehicle_id_vehicles_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quotations" ADD CONSTRAINT "quotations_gate_pass_id_gate_pass_id_fk" FOREIGN KEY ("gate_pass_id") REFERENCES "public"."gate_pass"("id") ON DELETE no action ON UPDATE no action;