import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from "@/lib/utils";
import { MRSLogo } from "@/components/jobcard/MRSLogo";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";

// Form validation schema
const invoiceFormSchema = z.object({
  jobCardId: z.number(),
  customerId: z.number(),
  subtotal: z.number().min(0),
  tax: z.number().min(0),
  total: z.number().min(0),
  status: z.string().default("UNPAID"),
  notes: z.string().optional(),
});

type InvoiceFormValues = z.infer<typeof invoiceFormSchema>;

type InvoiceFormProps = {
  jobCardId: number;
  onComplete: () => void;
};

const InvoiceForm = ({ jobCardId, onComplete }: InvoiceFormProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [taxRate, setTaxRate] = useState(0.07); // 7% default tax rate

  // Fetch job card details
  const { data: jobCard, isLoading: jobCardLoading } = useQuery({
    queryKey: [`/api/jobcards/${jobCardId}`],
  });

  // Fetch service items
  const { data: serviceItems, isLoading: serviceItemsLoading } = useQuery({
    queryKey: [`/api/jobcards/${jobCardId}/services`],
  });

  // Fetch part items
  const { data: partItems, isLoading: partItemsLoading } = useQuery({
    queryKey: [`/api/jobcards/${jobCardId}/parts`],
  });

  // Calculate totals
  const [subtotal, setSubtotal] = useState(0);
  const [tax, setTax] = useState(0);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    if (serviceItems && partItems) {
      const servicesTotal = serviceItems.reduce(
        (sum: number, item: any) => sum + item.price * item.quantity,
        0
      );
      const partsTotal = partItems.reduce(
        (sum: number, item: any) => sum + item.price * item.quantity,
        0
      );
      const newSubtotal = servicesTotal + partsTotal;
      const newTax = newSubtotal * taxRate;
      const newTotal = newSubtotal + newTax;

      setSubtotal(newSubtotal);
      setTax(newTax);
      setTotal(newTotal);

      // Update form values
      form.setValue("subtotal", newSubtotal);
      form.setValue("tax", newTax);
      form.setValue("total", newTotal);
    }
  }, [serviceItems, partItems, taxRate]);

  // Initialize form with default values
  const form = useForm<InvoiceFormValues>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: {
      jobCardId: jobCardId,
      customerId: 0,
      subtotal: 0,
      tax: 0,
      total: 0,
      status: "UNPAID",
      notes: "",
    },
  });

  // Update customer ID when job card data is loaded
  useEffect(() => {
    if (jobCard) {
      form.setValue("customerId", jobCard.customerId);
    }
  }, [jobCard, form]);

  // Create invoice mutation
  const createInvoiceMutation = useMutation({
    mutationFn: async (data: InvoiceFormValues) => {
      const response = await apiRequest("POST", "/api/invoices", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/invoices"] });
      queryClient.invalidateQueries({
        queryKey: [`/api/jobcards/${jobCardId}`],
      });
      toast({
        title: "Invoice created",
        description: "The invoice has been successfully created",
      });
      onComplete();
    },
    onError: (error) => {
      toast({
        title: "Error creating invoice",
        description: "There was an error creating the invoice. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = async (data: InvoiceFormValues) => {
    try {
      // Create invoice
      await createInvoiceMutation.mutateAsync(data);
    } catch (error) {
      console.error("Error creating invoice:", error);
    }
  };

  if (jobCardLoading || serviceItemsLoading || partItemsLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Generate Invoice</CardTitle>
          <CardDescription>Loading job details...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center">Loading invoice data...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Generate Invoice</CardTitle>
              <CardDescription>
                Create an invoice for job #{jobCard?.jobNumber}
              </CardDescription>
            </div>
            <MRSLogo />
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Job Information</h3>
                <p className="mt-1 text-lg font-medium">
                  #{jobCard?.jobNumber} - {jobCard?.description}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Customer</h3>
                <p className="mt-1 text-lg font-medium">
                  Customer #{jobCard?.customerId}
                </p>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Service Items</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Service</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead className="text-right">Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {serviceItems?.length ? (
                    serviceItems.map((item: any) => (
                      <TableRow key={`service-${item.id}`}>
                        <TableCell className="font-medium">
                          Service #{item.serviceId}
                        </TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{formatCurrency(item.price)}</TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(item.price * item.quantity)}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                        No service items
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Parts</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Part</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead className="text-right">Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {partItems?.length ? (
                    partItems.map((item: any) => (
                      <TableRow key={`part-${item.id}`}>
                        <TableCell className="font-medium">
                          Part #{item.partId}
                        </TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{formatCurrency(item.price)}</TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(item.price * item.quantity)}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                        No parts
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Totals</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal:</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Tax Rate:</span>
                  <div className="w-24">
                    <Select
                      defaultValue={(taxRate * 100).toString()}
                      onValueChange={(value) => setTaxRate(parseInt(value) / 100)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Tax %" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">0%</SelectItem>
                        <SelectItem value="5">5%</SelectItem>
                        <SelectItem value="7">7%</SelectItem>
                        <SelectItem value="10">10%</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax:</span>
                  <span>{formatCurrency(tax)}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-lg font-bold">
                  <span>Total:</span>
                  <span>{formatCurrency(total)}</span>
                </div>
              </div>
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Invoice Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional notes for the invoice..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="border-t px-6 py-4 flex justify-between">
            <Button type="button" variant="outline">
              Preview Invoice
            </Button>
            <Button
              type="submit"
              disabled={createInvoiceMutation.isPending}
            >
              {createInvoiceMutation.isPending
                ? "Generating..."
                : "Generate Invoice"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
};

export default InvoiceForm;
