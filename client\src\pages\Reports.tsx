import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { formatCurrency, formatDate } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  Cell,
} from "recharts";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Download, FileText, Users, Car, CreditCard, Activity } from "lucide-react";
import { format } from "date-fns";

const Reports = () => {
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date(),
  });
  const [selectedReport, setSelectedReport] = useState("servicePerformance");

  // Fetch all job cards
  const { data: jobCards, isLoading: jobsLoading } = useQuery({
    queryKey: ["/api/jobcards"],
  });

  // Fetch all technicians
  const { data: technicians, isLoading: techniciansLoading } = useQuery({
    queryKey: ["/api/technicians"],
  });

  // Fetch all invoices
  const { data: invoices, isLoading: invoicesLoading } = useQuery({
    queryKey: ["/api/invoices"],
  });

  // Fetch all customers
  const { data: customers, isLoading: customersLoading } = useQuery({
    queryKey: ["/api/customers"],
  });

  // Fetch all vehicles
  const { data: vehicles, isLoading: vehiclesLoading } = useQuery({
    queryKey: ["/api/vehicles"],
  });

  // Filter data by date range
  const filterByDateRange = (items: any[], dateField: string) => {
    return items?.filter((item) => {
      const itemDate = new Date(item[dateField]);
      const fromDate = dateRange.from ? new Date(dateRange.from) : null;
      const toDate = dateRange.to ? new Date(dateRange.to) : null;
      
      if (fromDate && toDate) {
        return itemDate >= fromDate && itemDate <= toDate;
      } else if (fromDate) {
        return itemDate >= fromDate;
      } else if (toDate) {
        return itemDate <= toDate;
      }
      
      return true;
    }) || [];
  };

  const filteredJobCards = filterByDateRange(jobCards || [], "createdAt");
  const filteredInvoices = filterByDateRange(invoices || [], "createdAt");

  // Prepare data for charts
  const getStatusDistribution = () => {
    const statusCounts: Record<string, number> = {};
    
    filteredJobCards.forEach((job: any) => {
      if (statusCounts[job.status]) {
        statusCounts[job.status]++;
      } else {
        statusCounts[job.status] = 1;
      }
    });
    
    return Object.entries(statusCounts).map(([status, count]) => ({
      name: status
        .split("_")
        .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
        .join(" "),
      value: count,
    }));
  };

  const getRevenueByService = () => {
    const total = filteredInvoices.reduce((sum: number, invoice: any) => sum + invoice.total, 0);
    const paid = filteredInvoices.filter((invoice: any) => invoice.status === "PAID").reduce((sum: number, invoice: any) => sum + invoice.total, 0);
    const unpaid = total - paid;
    
    return [
      { name: "Paid", value: paid },
      { name: "Unpaid", value: unpaid },
    ];
  };

  const getJobsOverTime = () => {
    const jobsByDate: Record<string, number> = {};
    
    filteredJobCards.forEach((job: any) => {
      const date = format(new Date(job.createdAt), "MMM d");
      if (jobsByDate[date]) {
        jobsByDate[date]++;
      } else {
        jobsByDate[date] = 1;
      }
    });
    
    return Object.entries(jobsByDate)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };

  const getTechnicianPerformance = () => {
    const techJobs: Record<number, { name: string; completed: number; inProgress: number }> = {};
    
    technicians?.forEach((tech: any) => {
      techJobs[tech.id] = {
        name: tech.name,
        completed: 0,
        inProgress: 0,
      };
    });
    
    filteredJobCards.forEach((job: any) => {
      if (job.technicianId && techJobs[job.technicianId]) {
        if (job.status === "COMPLETED" || job.status === "READY_FOR_PICKUP") {
          techJobs[job.technicianId].completed++;
        } else if (job.status === "IN_PROGRESS") {
          techJobs[job.technicianId].inProgress++;
        }
      }
    });
    
    return Object.values(techJobs);
  };

  // Calculate summary statistics
  const calculateSummaryStats = () => {
    const totalJobs = filteredJobCards.length;
    const completedJobs = filteredJobCards.filter((job: any) => 
      job.status === "COMPLETED" || job.status === "READY_FOR_PICKUP"
    ).length;
    const totalRevenue = filteredInvoices.reduce((sum: number, invoice: any) => sum + invoice.total, 0);
    const paidRevenue = filteredInvoices.filter((invoice: any) => invoice.status === "PAID").reduce((sum: number, invoice: any) => sum + invoice.total, 0);
    
    return {
      totalJobs,
      completedJobs,
      completionRate: totalJobs > 0 ? Math.round((completedJobs / totalJobs) * 100) : 0,
      totalRevenue,
      paidRevenue,
      collectionRate: totalRevenue > 0 ? Math.round((paidRevenue / totalRevenue) * 100) : 0,
    };
  };

  const stats = calculateSummaryStats();
  const isLoading = jobsLoading || techniciansLoading || invoicesLoading || customersLoading || vehiclesLoading;

  // Chart colors
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

  // Render loading state
  if (isLoading) {
    return (
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <h1 className="text-2xl font-semibold text-gray-900">Reports</h1>
          <div className="mt-6">
            <Card>
              <CardContent className="py-10">
                <div className="text-center">
                  <Activity className="h-10 w-10 text-gray-300 mx-auto animate-pulse" />
                  <p className="mt-4 text-gray-500">Loading report data...</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Reports</h1>
            <p className="mt-1 text-gray-600">
              View performance metrics and generate reports
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3 items-center">
            <div className="flex items-center space-x-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className="w-[240px] justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "LLL dd, y")} -{" "}
                          {format(dateRange.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(dateRange.from, "LLL dd, y")
                      )
                    ) : (
                      <span>Pick a date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={{
                      from: dateRange.from,
                      to: dateRange.to,
                    }}
                    onSelect={(range) => {
                      setDateRange({
                        from: range?.from,
                        to: range?.to,
                      });
                    }}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <Button variant="outline" className="flex items-center gap-1">
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-primary/10 rounded-md p-3">
                  <FileText className="h-6 w-6 text-primary" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Jobs
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {stats.totalJobs}
                    </div>
                    <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                      <span>{stats.completionRate}% completed</span>
                    </div>
                  </dd>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-amber-500/10 rounded-md p-3">
                  <CreditCard className="h-6 w-6 text-amber-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Revenue
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatCurrency(stats.totalRevenue)}
                    </div>
                    <div className="ml-2 flex items-baseline text-sm font-semibold text-amber-500">
                      <span>{stats.collectionRate}% collected</span>
                    </div>
                  </dd>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-green-500/10 rounded-md p-3">
                  <Users className="h-6 w-6 text-green-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Customers
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {customers?.length || 0}
                    </div>
                  </dd>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-gray-100 rounded-md p-3">
                  <Car className="h-6 w-6 text-gray-700" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Vehicles
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {vehicles?.length || 0}
                    </div>
                  </dd>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-6">
          <div className="mb-4">
            <Select
              value={selectedReport}
              onValueChange={setSelectedReport}
            >
              <SelectTrigger className="w-[250px]">
                <SelectValue placeholder="Select report" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="servicePerformance">Service Performance</SelectItem>
                <SelectItem value="revenueAnalysis">Revenue Analysis</SelectItem>
                <SelectItem value="technicianPerformance">Technician Performance</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {selectedReport === "servicePerformance" && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Job Status Distribution</CardTitle>
                  <CardDescription>
                    Overview of job cards by current status
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-72">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={getStatusDistribution()}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {getStatusDistribution().map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Jobs Over Time</CardTitle>
                  <CardDescription>
                    Number of jobs created over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-72">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={getJobsOverTime()}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="count"
                          name="Jobs"
                          stroke="#0078D4"
                          activeDot={{ r: 8 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {selectedReport === "revenueAnalysis" && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Status</CardTitle>
                  <CardDescription>
                    Distribution of paid vs unpaid invoices
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-72">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={getRevenueByService()}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, value, percent }) => 
                            `${name}: ${formatCurrency(value)} (${(percent * 100).toFixed(0)}%)`
                          }
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          <Cell fill="#0078D4" />
                          <Cell fill="#FFB900" />
                        </Pie>
                        <Tooltip formatter={(value) => formatCurrency(value as number)} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Invoices</CardTitle>
                  <CardDescription>
                    Latest invoices in the selected date range
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Invoice #</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredInvoices.slice(0, 5).map((invoice: any) => (
                          <TableRow key={invoice.id}>
                            <TableCell className="font-medium">
                              {invoice.invoiceNumber}
                            </TableCell>
                            <TableCell>{formatDate(invoice.createdAt)}</TableCell>
                            <TableCell>{formatCurrency(invoice.total)}</TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                invoice.status === "PAID" 
                                  ? "bg-green-100 text-green-800" 
                                  : "bg-yellow-100 text-yellow-800"
                              }`}>
                                {invoice.status}
                              </span>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {selectedReport === "technicianPerformance" && (
            <Card>
              <CardHeader>
                <CardTitle>Technician Performance</CardTitle>
                <CardDescription>
                  Jobs completed and in progress by technician
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={getTechnicianPerformance()}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="completed" name="Completed Jobs" fill="#0078D4" />
                      <Bar dataKey="inProgress" name="In Progress Jobs" fill="#FFB900" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default Reports;
