
function getToken(): string | null {
    // Replace with your token retrieval logic (e.g., from localStorage)
    return localStorage.getItem('token');
}

// Set API base URL from environment variables
// In Vite, client-side env variables are accessed via import.meta.env, not process.env
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

console.log('API Base URL:', API_BASE_URL); // Debug log

export async function fetchAPI(
    endpointOrMethod: string,
    endpointOrOptions?: string | RequestInit,
    data?: any
): Promise<Response> {
    let endpoint: string;
    let options: RequestInit = {};

    // Handle different parameter patterns
    if (typeof endpointOrOptions === 'string') {
        // Pattern: fetchAPI(method, endpoint, data)
        endpoint = endpointOrOptions;
        options.method = endpointOrMethod;
        if (data) {
            options.body = JSON.stringify(data);
        }
    } else {
        // Pattern: fetchAPI(endpoint, options)
        endpoint = endpointOrMethod;
        if (endpointOrOptions) {
            options = endpointOrOptions;
        }
    }

    const token = getToken();
    const headers: HeadersInit = {
        ...(options.headers || {}),
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
        'Content-Type': 'application/json',
    };

    // Make sure endpoint starts with a slash if API_BASE_URL doesn't end with one
    const url = endpoint.startsWith('/') 
        ? `${API_BASE_URL}${endpoint}` 
        : `${API_BASE_URL}/${endpoint}`;

    console.log('Fetching URL:', url); // Debug log

    const response = await fetch(url, {
        ...options,
        headers,
    });

    return response;
}
