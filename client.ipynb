{"cells": [{"cell_type": "code", "execution_count": 3, "id": "8cd76b23", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["409 {\"error\":\"User already exists\"}\n"]}], "source": ["import requests\n", "from requests import Session\n", "\n", "session = Session()\n", "\n", "url = 'http://localhost:5000/api/register'\n", "\n", "r = session.post(url, json = {\n", "    'username': '<PERSON><PERSON><PERSON>',\n", "    'password': '<PERSON><PERSON><PERSON>@123'\n", "})\n", "print(r.status_code, r.text)"]}, {"cell_type": "code", "execution_count": 10, "id": "89878870", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200 [{\"customerName\":\"tyjdfg\"}]\n"]}], "source": ["customer_list_url = 'http://localhost:5000/api/gate-pass/customer/list'\n", "customer_list_resp = session.get(customer_list_url)\n", "print(customer_list_resp.status_code, customer_list_resp.text)"]}, {"cell_type": "code", "execution_count": 12, "id": "4e4e77c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200 [{\"vehicleNumber\":\"234\",\"make_model\":\" asdfer\",\"mileage\":\"wef\",\"status\":\"PENDING\",\"estimatedDeliveryDate\":\"2025-06-26T18:30:00.000Z\"}]\n"]}], "source": ["customer_vehicle_list_url = 'http://localhost:5000/api/gate-pass/customer/vehicle/list?customerName=tyjdfg'\n", "customer_vehicle_list_resp = session.get(customer_vehicle_list_url)\n", "print(customer_vehicle_list_resp.status_code, customer_vehicle_list_resp.text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}}, "nbformat": 4, "nbformat_minor": 5}