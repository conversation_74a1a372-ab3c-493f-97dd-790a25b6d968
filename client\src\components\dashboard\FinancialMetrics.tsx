import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { formatCurrency } from "@/lib/utils";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from "recharts";
import { TrendingUp, TrendingDown, DollarSign, CreditCard, ShoppingBag, Users } from "lucide-react";

const financialData = [
  { name: 'Jan', grossRevenue: 35000, netProfit: 12500, expenses: 22500 },
  { name: 'Feb', grossRevenue: 42000, netProfit: 16800, expenses: 25200 },
  { name: 'Mar', grossRevenue: 38000, netProfit: 14800, expenses: 23200 },
  { name: 'Apr', grossRevenue: 45000, netProfit: 18900, expenses: 26100 },
  { name: 'May', grossRevenue: 50000, netProfit: 21000, expenses: 29000 },
  { name: 'Jun', grossRevenue: 55000, netProfit: 24200, expenses: 30800 },
];

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  isPositive: boolean;
  icon: React.ReactNode;
}

const MetricCard = ({ title, value, change, isPositive, icon }: MetricCardProps) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-500">{title}</p>
            <h3 className="text-2xl font-bold mt-1">{value}</h3>
            <div className="flex items-center mt-1">
              {isPositive ? (
                <TrendingUp className="w-4 h-4 mr-1 text-green-500" />
              ) : (
                <TrendingDown className="w-4 h-4 mr-1 text-red-500" />
              )}
              <span className={`text-sm font-medium ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
                {change}
              </span>
            </div>
          </div>
          <div className="p-3 bg-primary/10 rounded-full">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const FinancialMetrics = () => {
  return (
    <div className="mt-8">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">Executive Dashboard</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <MetricCard
          title="Gross Revenue (MTD)"
          value={formatCurrency(55000)}
          change="+8.2% vs last month"
          isPositive={true}
          icon={<DollarSign className="h-6 w-6 text-primary" />}
        />
        <MetricCard
          title="Net Profit (MTD)"
          value={formatCurrency(24200)}
          change="+14.7% vs last month"
          isPositive={true}
          icon={<CreditCard className="h-6 w-6 text-primary" />}
        />
        <MetricCard
          title="Parts Revenue (MTD)"
          value={formatCurrency(22000)}
          change="+5.3% vs last month"
          isPositive={true}
          icon={<ShoppingBag className="h-6 w-6 text-primary" />}
        />
        <MetricCard
          title="Labor Revenue (MTD)"
          value={formatCurrency(33000)}
          change="+10.5% vs last month"
          isPositive={true}
          icon={<Users className="h-6 w-6 text-primary" />}
        />
      </div>

      <Tabs defaultValue="revenue" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="revenue">Revenue Analysis</TabsTrigger>
          <TabsTrigger value="profit">Profit Margins</TabsTrigger>
          <TabsTrigger value="trends">Monthly Trends</TabsTrigger>
        </TabsList>
        
        <TabsContent value="revenue" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Breakdown (Last 6 Months)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={financialData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `AED ${value/1000}k`} />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Bar dataKey="grossRevenue" name="Gross Revenue" fill="#0ea5e9" />
                    <Bar dataKey="expenses" name="Expenses" fill="#f43f5e" />
                    <Bar dataKey="netProfit" name="Net Profit" fill="#10b981" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="profit" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Profit Margin (Last 6 Months)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={financialData.map(item => ({
                      ...item,
                      profitMargin: (item.netProfit / item.grossRevenue) * 100
                    }))}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis 
                      tickFormatter={(value) => `${value}%`}
                      domain={[30, 50]}
                    />
                    <Tooltip formatter={(value) => `${typeof value === 'number' ? value.toFixed(2) : value}%`} />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="profitMargin"
                      name="Profit Margin"
                      stroke="#8884d8"
                      activeDot={{ r: 8 }}
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue & Profit Trends (Last 6 Months)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={financialData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `AED ${value/1000}k`} />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="grossRevenue"
                      name="Gross Revenue"
                      stroke="#0ea5e9"
                      activeDot={{ r: 8 }}
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="netProfit"
                      name="Net Profit"
                      stroke="#10b981"
                      activeDot={{ r: 8 }}
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FinancialMetrics;