import { useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useLocation } from "wouter";
import { Loader2 } from "lucide-react";
import Sidebar from "./Sidebar";
import Header from "./Header";

export default function ProtectedLayout() {
  const { user, isLoading } = useAuth();
  const [location, navigate] = useLocation();
  
  // Check if user is logged in
  useEffect(() => {
    if (!isLoading && !user) {
      navigate("/auth");
    }
  }, [user, isLoading, navigate]);
  
  // Show loading spinner while checking auth status
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  // If user is not logged in, don't render anything
  if (!user) {
    return null;
  }
  
  // Render the main layout with sidebar, header, and content
  return (
    <>
      <Sidebar open={false} setOpen={() => {}} />
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        <Header setSidebarOpen={() => {}} />
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          {/* Render the current route content */}
        </main>
      </div>
    </>
  );
}