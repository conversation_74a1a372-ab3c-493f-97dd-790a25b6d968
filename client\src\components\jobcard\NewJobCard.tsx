// import { useState, useEffect } from "react";
import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Link, useLocation } from "wouter";
import { calculateTotalCost, formatCurrency } from "@/lib/utils";

import { fetchAPI } from "@/lib/api";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { MRSLogo, MRSLogoSmall } from "./MRSLogo";
import { VehicleDiagram, VehicleInventoryChecklist } from "./VehicleDiagram";

// ispending for submit button

// Define schema for job card form
const jobCardSchema = z.object({
  vehicleId: z.number().default(0),
  description: z.string().min(1, "Job description is required"),
  estimatedCompletionDate: z.date().optional().nullable(),
  notes: z.string().optional(),
  isInsuranceClaim: z.boolean().default(false),
  isPvtClaim: z.boolean().default(false),
  status: z.string().default("PENDING"),
  // Custom fields for the form UI
  vehicleNumber: z.string().optional(),
  make_model: z.string().optional(),
  chassisNumber: z.string().optional(),
  engineNumber: z.string().optional(),
  ownerName: z.string().optional(),
  mileage: z.string().optional(),
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),
  customerEmail: z.string().optional(),
  registrationNumber: z.string().optional(),
  isRegCard: z.boolean().optional(),
  canvasJson: z.any().optional(),
});
type JobCardFormValues = z.infer<typeof jobCardSchema>;

// type JobCardApiRequest = {
//   vehicleId: number;
//   description: string;
//   technicianId?: number;
//   estimatedCompletionDate?: string;
//   notes?: string;
//   isInsuranceClaim: boolean;
//   isPvtClaim: boolean;
//   estimatedCost: number;
//   status: string;
//   initialPhotos?: string[];
// };

type ServiceItem = {
  serviceId: number;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
};

type PartItem = {
  partId: number;
  name: string;
  quantity: number;
  price: number;
  partNumber: string;
  notes?: string;
};
interface FilteredOption {
  id: number;
  registrationNumber: string;
  make: string;
  model: string;
  engineNumber: string;
  chassisNumber: string;
}

type JobCardPaperFormProps = {
  vehicleId?: string;
};

let damageCanvasJson: any = null;
export const NewJobCardForm = ({
  vehicleId: vehicleIdParam,
}: JobCardPaperFormProps) => {
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Vehicle and Customer Data
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredOptions, setFilteredOptions] = useState<FilteredOption[]>([]);

  // Service and Parts Data
  // const [selectedService, setSelectedService] = useState<ServiceItem | null>(
  //   null
  // );
  // const [selectedServices, setSelectedServices] = useState<ServiceItem[]>([]);
  // const [selectedPart, setSelectedPart] = useState<PartItem | null>(null);
  // const [selectedParts, setSelectedParts] = useState<PartItem[]>([]);

  // Vehicle photo uploads
  const [photos, setPhotos] = useState<File[]>([]);

  // Vehicle damages
  const [selectedDamages, setSelectedDamages] = useState<string[]>([]);
  
  const [isPending, setIsPending] = useState(false);

  // Job card ID for edit mode (undefined for create mode)
  const [jobCardId, setJobCardId] = useState<number | undefined>(undefined);

  // Define React Hook Form
  const form = useForm<any>({
    resolver: zodResolver(jobCardSchema),
    defaultValues: {
      vehicleId: 0,
      description: "",
      estimatedCompletionDate: null,
      notes: "",
      isInsuranceClaim: false,
      isPvtClaim: false,
      status: "PENDING",
      vehicleNumber: "",
      make_model: "",
      chassisNumber: "",
      engineNumber: "",
      ownerName: "",
      mileage: "",
      customerName: "", // Ensure this is initialized as empty string
      customerPhone: "",
      customerEmail: "",
      registrationNumber: "",
      isRegCard: false,
      canvasJson: null,
    },
  });

  // If vehicleId param is provided, fetch and set vehicle data
  useEffect(() => {
    if (vehicleIdParam) {
      const vehicleId = parseInt(vehicleIdParam);
      form.setValue("vehicleId", vehicleId);
      
      // Remove the reference to vehicles which is undefined
      // No need to find vehicle from fetched data here since we're using search API
    }
  }, [vehicleIdParam, form]);

  // Handle damage diagram clicks
  const handleToggleDamage = (part: string) => {
    if (selectedDamages.includes(part)) {
      setSelectedDamages(selectedDamages.filter((p) => p !== part));
    } else {
      setSelectedDamages([...selectedDamages, part]);
    }
  };

  const handleAttachDamage = (dataUrl: string, canvasJson: JSON) => {
    // This function can be used to handle the attached damage image
    // For now, we just log it
    console.log("Attached damage image:", dataUrl, canvasJson);

    damageCanvasJson = canvasJson;

    toast({
      title: "Damage Image Attached",
      description: "The damage image has been successfully attached.",
      variant: "default",
    });
  };

  const searchLicensePlate = async (term: string) => {
    console.log("Searching for license plate:", term);

    try {
      const response = await fetchAPI(
        `/gate-pass/vehicle/search?keyword=${encodeURI(term)}`,
        {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        }
      );
      if (!response.ok) {
        setFilteredOptions([]);
        return;
      }
      const data = await response.json();
      // Only search by license plate, expect data to be an array of vehicles
      const options: FilteredOption[] = [];
      if (Array.isArray(data)) {
        data.forEach((vehicle: any) => {
          options.push({
            id: vehicle.id,
            registrationNumber: vehicle.vehicleRegistrationNumber,
            make: vehicle.make,
            model: vehicle.model_model,
            engineNumber: vehicle.engineNumber,
            chassisNumber: vehicle.chassisNumber,
          });
        });
      }
      setFilteredOptions(options);
    } catch (error) {
      setFilteredOptions([]);
    }
  };

  const onSubmit = async (data: any) => {
    console.log("onSubmit called - Submitting job card with data:", data);
    console.log("Form errors:", form.formState.errors);
    setIsPending(true);
    
    // Explicitly check and log customer name
    const customerName = data.customerName || "";
    console.log("Customer name before submission:", customerName);
    
    try {
      // create object to hold the job card data
      const jobCardData = {
        vehicleId: data.vehicleId || 0,
        vehicleNumber: data.vehicleNumber || data.registrationNumber || "",
        customerName: customerName, // Use the explicitly checked value
        customerContact: data.customerPhone || "",
        owner_name: data.ownerName || "User",
        serviceType: data.serviceType || "PRIVATE",
        make_model: data.make_model || "",
        customer_email: data.customerEmail || "",
        mileage: data.mileage || "",
        status: "PENDING",
        estimatedDeliveryDate: data.estimatedCompletionDate
          ? new Date(data.estimatedCompletionDate)
          : null,
        description: data.description || "",
        engine_number: data.engineNumber || "",
        chassis_number: data.chassisNumber || "",
        remarks: data.notes || "",
        canvasJson: JSON.stringify(damageCanvasJson || null),
      };

      // Log the final data object being sent
      console.log("Final jobCardData being sent:", JSON.stringify(jobCardData));

      // Make the API request
      const response = await fetchAPI("/gate-pass/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(jobCardData),
      });

      // Check if the response is ok
      if (!response.ok) {
        const errorData = await response.json();
        console.log("API error data:", errorData);
        throw new Error(errorData.message || "Failed to create job card");
      }
      
      // If successful, parse the response
      const result = await response.json();
      console.log("Job card created successfully:", result);
      toast({
        title: "Job Card Created Successfully",
        description: `Job Card ${result.id} has been created successfully`,
        variant: "default",
      });

      navigate("/gate-pass");
    } catch (error) {
      console.error("Error creating job card:", error);
      toast({
        title: "Error creating job card",
        description: error instanceof Error ? error.message : "Failed to create job card",
        variant: "destructive",
      });
    } finally {
      setIsPending(false);
    }
  };

  return (
    <div className="content">
      <div className="header">
        M R S AUTO MAINTENANCE L.L.C
      </div>
      <div className="sub-header">
        Tel: +971 55 994 1284, +971 55 994 1285 | Email: <EMAIL>
      </div>
      <h2 style={{ textAlign: "center",fontWeight: "bold",marginBottom: "30px" }}>JOB CARD</h2>
      <table className="details-table">
        <tbody>
          <tr>
            <td><strong>JOBCARD ID:</strong> {jobCardId || ""}</td>
            <td><strong>Date:</strong> {new Date().toLocaleDateString()}</td>
            <td><strong>Time:</strong> {new Date().toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}</td>
          </tr>
          <tr>
            <td><strong>Driver Name:</strong> {form.getValues("customerName") || ""}</td>
            <td><strong>Make & Model:</strong> {form.getValues("make_model") || ""}</td>
            <td><strong>Assigned to:</strong> {"Unassigned"}</td>
          </tr>
          <tr>
            <td><strong>Chassis No:</strong> {form.getValues("chassisNumber") || ""}</td>
            <td><strong>Engine No:</strong> {form.getValues("engineNumber") || ""}</td>
            <td><strong>Reg No:</strong> {form.getValues("registrationNumber") || ""}</td>
          </tr>
          <tr>
            <td><strong>Delivery Date:</strong> {form.getValues("estimatedCompletionDate") ? 
              new Date(form.getValues("estimatedCompletionDate")).toLocaleDateString() : ""}</td>
            <td><strong>Color:</strong> {""}</td>
            <td><strong>ODO Meter:</strong> {form.getValues("mileage") || ""}</td>
          </tr>
          <tr>
            <td><strong>Mobile No:</strong> {form.getValues("customerPhone") || ""}</td>
            <td colSpan={2}><strong>Veh. Reg. Card:</strong> {form.getValues("isRegCard") ? "Y" : "N"}</td>
          </tr>
        </tbody>
      </table>
      <br />
      <table className="billing-table">
        <thead>
          <tr>
            <th style={{ width: "5%" }}>S.N</th>
            <th style={{ width: "55%" }}>Description</th>
            <th style={{ width: "10%" }}>Qty</th>
            <th style={{ width: "10%" }}>Unit</th>
            <th style={{ width: "10%" }}>Price</th>
            <th style={{ width: "10%" }}>Amount (AED)</th>
          </tr>
        </thead>
        <tbody>
          {/* This would be populated with actual service/parts data */}
          <tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>
          <tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>
          <tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>
          <tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>
        </tbody>
      </table>
      <br />
      <table className="summary-table">
        <tbody>
          <tr>
            <td className="label">Total</td>
            <td className="value">0.00</td>
          </tr>
          <tr>
            <td className="label">Less : Excess</td>
            <td className="value">0.00</td>
          </tr>
          <tr>
            <td className="label">Less : Discount</td>
            <td className="value">0.00</td>
          </tr>
          <tr>
            <td className="label">Add : VAT @ 5.00%</td>
            <td className="value">0.00</td>
          </tr>
          <tr className="grand-total-row">
            <td className="label">Grand Total</td>
            <td className="value">0.00</td>
          </tr>
        </tbody>
      </table>
      <br /><strong>Dirhams Zero Only</strong>
      <div className="declaration">
        <strong>DECLARATION</strong><br />
        I hereby authorize your garage to repair my vehicle.<br /><br />
        Name: ______________________ <br />
        Signature: __________________
      </div>
      <div className="signature">
        for <strong>M R S Auto Maintenance LLC</strong><br />
        Authorised Signatory
      </div>
    </div>
  );
};

// Add default export at the end of the file
export default NewJobCardForm;

// add css for this page 
import "./NewJobCard.css";
