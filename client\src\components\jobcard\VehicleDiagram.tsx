import React, { useState } from "react";

import bodyDamageReport from "@/assets/car-body-sketch.jpg";
import ImageEditor from "./ImageEditor"
import { Json } from "node_modules/drizzle-zod/utils.d.mts";

interface VehicleDiagramProps {
  onAttach?: (dataUrl: string, canvasJson: JSON) => void; // Add this prop
}

export function VehicleDiagram({ onAttach }: VehicleDiagramProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [imageSrc, setImageSrc] = useState(bodyDamageReport);

  const handleAttach = (dataUrl: string, canvasJson: JSON) => {
    setImageSrc(dataUrl);      // Update the image shown in UI
    setIsEditing(false);       // Close the editor
    if (onAttach) {
      onAttach(dataUrl, canvasJson);       // Notify parent
    }
  };

  return (
    <div>
      <img
        src={imageSrc}
        alt="Thumbnail"
        onClick={() => setIsEditing(true)}
        style={{ width: "300px", cursor: "pointer" }}
      />

      {isEditing && (
        <ImageEditor
          imageUrl={imageSrc}
          onClose={() => setIsEditing(false)}
          onAttach={handleAttach}   // Pass the handler
        />
      )}
    </div>
  );
}