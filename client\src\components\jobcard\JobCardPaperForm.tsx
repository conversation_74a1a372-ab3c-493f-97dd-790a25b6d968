// import { useState, useEffect } from "react";
import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Link, useLocation } from "wouter";
import { calculateTotalCost, formatCurrency } from "@/lib/utils";

import { fetchAPI } from "@/lib/api";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { MRSLogo, MRSLogoSmall } from "./MRSLogo";
import { VehicleDiagram, VehicleInventoryChecklist } from "./VehicleDiagram";

// ispending for submit button

// Define schema for job card form
const jobCardSchema = z.object({
  vehicleId: z.number().default(0),
  description: z.string().min(1, "Job description is required"),
  estimatedCompletionDate: z.date().optional().nullable(),
  notes: z.string().optional(),
  isInsuranceClaim: z.boolean().default(false),
  isPvtClaim: z.boolean().default(false),
  status: z.string().default("PENDING"),
  // Custom fields for the form UI
  vehicleNumber: z.string().optional(),
  make_model: z.string().optional(),
  chassisNumber: z.string().optional(),
  engineNumber: z.string().optional(),
  ownerName: z.string().optional(),
  mileage: z.string().optional(),
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),
  customerEmail: z.string().optional(),
  registrationNumber: z.string().optional(),
  isRegCard: z.boolean().optional(),
  canvasJson: z.any().optional(),
});
type JobCardFormValues = z.infer<typeof jobCardSchema>;

// type JobCardApiRequest = {
//   vehicleId: number;
//   description: string;
//   technicianId?: number;
//   estimatedCompletionDate?: string;
//   notes?: string;
//   isInsuranceClaim: boolean;
//   isPvtClaim: boolean;
//   estimatedCost: number;
//   status: string;
//   initialPhotos?: string[];
// };

type ServiceItem = {
  serviceId: number;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
};

type PartItem = {
  partId: number;
  name: string;
  quantity: number;
  price: number;
  partNumber: string;
  notes?: string;
};
interface FilteredOption {
  id: number;
  registrationNumber: string;
  make: string;
  model: string;
  engineNumber: string;
  chassisNumber: string;
}

type JobCardPaperFormProps = {
  vehicleId?: string;
};

let damageCanvasJson: any = null;
export const JobCardPaperForm = ({
  vehicleId: vehicleIdParam,
}: JobCardPaperFormProps) => {
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Vehicle and Customer Data
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredOptions, setFilteredOptions] = useState<FilteredOption[]>([]);

  // Service and Parts Data
  // const [selectedService, setSelectedService] = useState<ServiceItem | null>(
  //   null
  // );
  // const [selectedServices, setSelectedServices] = useState<ServiceItem[]>([]);
  // const [selectedPart, setSelectedPart] = useState<PartItem | null>(null);
  // const [selectedParts, setSelectedParts] = useState<PartItem[]>([]);

  // Vehicle photo uploads
  const [photos, setPhotos] = useState<File[]>([]);

  // Vehicle damages
  const [selectedDamages, setSelectedDamages] = useState<string[]>([]);
  
  const [isPending, setIsPending] = useState(false);

  // Job card ID for edit mode (undefined for create mode)
  const [jobCardId, setJobCardId] = useState<number | undefined>(undefined);

  // Define React Hook Form
  const form = useForm<any>({
    resolver: zodResolver(jobCardSchema),
    defaultValues: {
      vehicleId: 0,
      description: "",
      estimatedCompletionDate: null,
      notes: "",
      isInsuranceClaim: false,
      isPvtClaim: false,
      status: "PENDING",
      vehicleNumber: "",
      make_model: "",
      chassisNumber: "",
      engineNumber: "",
      ownerName: "",
      mileage: "",
      customerName: "", // Ensure this is initialized as empty string
      customerPhone: "",
      customerEmail: "",
      registrationNumber: "",
      isRegCard: false,
      canvasJson: null,
    },
  });

  // If vehicleId param is provided, fetch and set vehicle data
  useEffect(() => {
    if (vehicleIdParam) {
      const vehicleId = parseInt(vehicleIdParam);
      form.setValue("vehicleId", vehicleId);
      
      // Remove the reference to vehicles which is undefined
      // No need to find vehicle from fetched data here since we're using search API
    }
  }, [vehicleIdParam, form]);

  // Handle damage diagram clicks
  const handleToggleDamage = (part: string) => {
    if (selectedDamages.includes(part)) {
      setSelectedDamages(selectedDamages.filter((p) => p !== part));
    } else {
      setSelectedDamages([...selectedDamages, part]);
    }
  };

  const handleAttachDamage = (dataUrl: string, canvasJson: JSON) => {
    // This function can be used to handle the attached damage image
    // For now, we just log it
    console.log("Attached damage image:", dataUrl, canvasJson);

    damageCanvasJson = canvasJson;

    toast({
      title: "Damage Image Attached",
      description: "The damage image has been successfully attached.",
      variant: "default",
    });
  };

  const searchLicensePlate = async (term: string) => {
    console.log("Searching for license plate:", term);

    try {
      const response = await fetchAPI(
        `/gate-pass/vehicle/search?keyword=${encodeURI(term)}`,
        {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        }
      );
      if (!response.ok) {
        setFilteredOptions([]);
        return;
      }
      const data = await response.json();
      // Only search by license plate, expect data to be an array of vehicles
      const options: FilteredOption[] = [];
      if (Array.isArray(data)) {
        data.forEach((vehicle: any) => {
          options.push({
            id: vehicle.id,
            registrationNumber: vehicle.vehicleRegistrationNumber,
            make: vehicle.make,
            model: vehicle.model_model,
            engineNumber: vehicle.engineNumber,
            chassisNumber: vehicle.chassisNumber,
          });
        });
      }
      setFilteredOptions(options);
    } catch (error) {
      setFilteredOptions([]);
    }
  };

  const onSubmit = async (data: any) => {
    console.log("onSubmit called - Submitting job card with data:", data);
    console.log("Form errors:", form.formState.errors);
    setIsPending(true);
    
    // Explicitly check and log customer name
    const customerName = data.customerName || "";
    console.log("Customer name before submission:", customerName);
    
    try {
      // create object to hold the job card data
      const jobCardData = {
        vehicleId: data.vehicleId || 0,
        vehicleNumber: data.vehicleNumber || data.registrationNumber || "",
        customerName: customerName, // Use the explicitly checked value
        customerContact: data.customerPhone || "",
        owner_name: data.ownerName || "User",
        serviceType: data.serviceType || "PRIVATE",
        make_model: data.make_model || "",
        customer_email: data.customerEmail || "",
        mileage: data.mileage || "",
        status: "PENDING",
        estimatedDeliveryDate: data.estimatedCompletionDate
          ? new Date(data.estimatedCompletionDate)
          : null,
        description: data.description || "",
        engine_number: data.engineNumber || "",
        chassis_number: data.chassisNumber || "",
        remarks: data.notes || "",
        canvasJson: JSON.stringify(damageCanvasJson || null),
      };

      // Log the final data object being sent
      console.log("Final jobCardData being sent:", JSON.stringify(jobCardData));

      // Make the API request
      const response = await fetchAPI("/gate-pass/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(jobCardData),
      });

      // Check if the response is ok
      if (!response.ok) {
        const errorData = await response.json();
        console.log("API error data:", errorData);
        throw new Error(errorData.message || "Failed to create job card");
      }
      
      // If successful, parse the response
      const result = await response.json();
      console.log("Job card created successfully:", result);
      toast({
        title: "Job Card Created Successfully",
        description: `Job Card ${result.id} has been created successfully`,
        variant: "default",
      });

      navigate("/gate-pass");
    } catch (error) {
      console.error("Error creating job card:", error);
      toast({
        title: "Error creating job card",
        description: error instanceof Error ? error.message : "Failed to create job card",
        variant: "destructive",
      });
    } finally {
      setIsPending(false);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-8 print:space-y-0 max-w-4xl mx-auto print:mx-0 p-2 print:p-0"
      >
        {/* Print-friendly header that shows on print only */}
        <div className="hidden print:block border-2 border-blue-700 p-4">
          <div className="grid grid-cols-3 items-center">
            <div className="col-span-1">
              <div className="text-sm">
                <div className="font-bold">GATE PASS NO.</div>
                <div>{form.getValues().id || "NEW"}</div>
                <div className="font-bold mt-2">CUST. NAME:</div>
                <div>{form.getValues("name") || ""}</div>
                <div className="font-bold mt-2">CAR NO.</div>
                <div>
                  {form.getValues("registrationNumber") || ""}
                </div>
                <div className="font-bold mt-2">RELEASED BY</div>
                <div></div>
              </div>
            </div>
            <div className="col-span-1 flex justify-center items-center flex-col">
              <div className="text-2xl font-bold text-blue-700 mb-2">
                GATE PASS
              </div>
              <div className="mb-2">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-blue-700 text-white flex items-center justify-center font-bold">
                    M
                  </div>
                  <div className="w-8 h-8 rounded-full bg-red-600 text-white flex items-center justify-center font-bold mx-1">
                    R
                  </div>
                  <div className="w-8 h-8 rounded-full bg-blue-700 text-white flex items-center justify-center font-bold">
                    S
                  </div>
                </div>
                <div className="text-blue-700 text-xs italic mt-1 text-center">
                  Your Most Reliable Partner
                </div>
              </div>
            </div>
            <div className="col-span-1">
              <div className="text-sm">
                <div className="font-bold">DATE</div>
                <div>{new Date().toLocaleDateString("en-US")}</div>
                <div className="font-bold mt-2">MOBILE NO.</div>
                <div>{form.getValues("phone") || ""}</div>
                <div className="font-bold mt-2">JOB NO.</div>
                <div></div>
                <div className="font-bold mt-2">CUSTOMER'S SIGN.</div>
                <div></div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Job Card Form */}
        <div className="bg-white border-2 border-blue-700 rounded-md overflow-hidden print:rounded-none">
          {/* Header with job card title and MRS logo */}
          <div className="bg-gray-50 p-4 border-b-2 border-blue-700 flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-blue-700">
                {!vehicleIdParam ? "Create New Job Card" : "Edit Job Card"}
              </h1>
              <div className="text-sm text-gray-500 ml-2">
                {new Date().toLocaleDateString()}
              </div>
            </div>
            <div className="hidden md:block">
              <MRSLogo />
            </div>
          </div>

          {/* Quick Search - Only shown in create mode, not in edit mode and not in print */}
          {!vehicleIdParam && (
            <div className="p-4 border-b border-gray-200 print:hidden">
              <div className="mb-4">
                <FormLabel htmlFor="search" className="text-sm font-bold">
                  Quick Search
                </FormLabel>
                <div className="flex space-x-2">
                  <Input
                    id="search"
                    type="text"
                    placeholder="Search by license plate"
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      searchLicensePlate(e.target.value);
                    }}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      toast({
                        title: "Refreshed data",
                        description:
                          "Vehicle and customer lists have been updated",
                      });
                    }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    Refresh
                  </Button>
                </div>

                {filteredOptions.length > 0 && (
                  <div className="relative mt-1">
                    <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                      {filteredOptions.map((option) => (
                        <li
                          key={option.id}
                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                          onClick={async () => {
                            // Set vehicle data
                            form.setValue("vehicleId", option.id);
                            form.setValue(
                              "registrationNumber",
                              option.registrationNumber
                            );
                            form.setValue("vehicleNumber", option.registrationNumber);
                            form.setValue(
                              "make_model",
                              `${option.make} ${option.model}`
                            );
                            form.setValue("engineNumber", option.engineNumber);
                            form.setValue("chassisNumber", option.chassisNumber);

                            // Fetch customer data from the most recent gate pass for this vehicle
                            try {
                              const response = await fetchAPI(`/gate-pass/vehicle/${option.id}/latest-customer`, {
                                method: "GET",
                                headers: { "Content-Type": "application/json" },
                              });

                              if (response.ok) {
                                const customerData = await response.json();
                                console.log("Fetched customer data:", customerData);

                                // Populate customer fields if data exists
                                if (customerData.customerName) {
                                  form.setValue("customerName", customerData.customerName);
                                  console.log("Set customer name to:", customerData.customerName);
                                }
                                if (customerData.email) {
                                  form.setValue("customerEmail", customerData.email);
                                  console.log("Set customer email to:", customerData.email);
                                }
                                if (customerData.phone) {
                                  form.setValue("customerPhone", customerData.phone);
                                  console.log("Set customer phone to:", customerData.phone);
                                }
                                if (customerData.ownerName) {
                                  form.setValue("ownerName", customerData.ownerName);
                                  console.log("Set owner name to:", customerData.ownerName);
                                }
                              }
                            } catch (error) {
                              console.log("No previous customer data found for this vehicle:", error);
                              // Don't show error to user, just continue without customer data
                            }

                            setSearchTerm(option.registrationNumber);
                            setFilteredOptions([]);
                          }}
                        >
                          {option.registrationNumber ||
                            option.make + " " + option.model}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Main card content - styled to match the paper form */}
          <div className="p-4">
            {/* Job card top section with two columns */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              {/* Left column */}
              <div className="col-span-1 border border-blue-700 rounded p-3">
                <div className="grid grid-cols-2 gap-2">
                  {/* <div className="col-span-2">
                    <div className="font-bold text-blue-700 mb-1 text-sm">JOB CARD NO.</div>
                    <FormField
                      control={form.control}
                      name="jobCardNumber"
                      render={({ field }) => (
                        <FormItem className="mb-2">
                          <FormControl>
                            <Input
                              className="h-8 text-sm border-blue-200 font-bold"
                              placeholder="Job Card Number"
                              defaultValue={form.getValues().id ? form.getValues().id : "NEW"}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div> */}

                  <div className="col-span-2">
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      DATE IN
                    </div>
                    <div className="text-sm mb-2">
                      {new Date().toLocaleDateString()}
                    </div>
                  </div>

                  <div className="col-span-2">
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      PVT / INSURANCE
                    </div>
                    <div className="flex gap-4 text-sm mb-2">
                      <FormField
                        control={form.control}
                        name="isPvtClaim"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(checked) => {
                                  field.onChange(checked);
                                  if (checked) {
                                    form.setValue("isInsuranceClaim", false);
                                  }
                                }}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal">
                              Pvt
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="isInsuranceClaim"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(checked) => {
                                  field.onChange(checked);
                                  if (checked) {
                                    form.setValue("isPvtClaim", false);
                                  }
                                }}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal">
                              Insurance
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className="col-span-2">
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      CUST.NAME
                    </div>
                    <FormField
                      control={form.control}
                      name="customerName"
                      render={({ field }) => (
                        <FormItem className="mb-2">
                          <FormControl>
                            <Input
                              className="h-8 text-sm border-blue-200"
                              placeholder="Customer Name"
                              {...field}
                              value={field.value || ""}
                              onChange={(e) => {
                                field.onChange(e.target.value);
                                // Log the value to verify it's being set
                                console.log("Customer name changed to:", e.target.value);
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="col-span-2">
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      VEHICLE REG.NO.
                    </div>
                    <FormField
                      control={form.control}
                      name="registrationNumber"
                      render={({ field }) => (
                        <FormItem className="mb-2">
                          <FormControl>
                            <Input
                              className="h-8 text-sm border-blue-200"
                              placeholder="Vehicle Registration Number"
                              {...field}
                              onChange={(e) => {
                                field.onChange(e);
                                // Also update vehicleNumber to match registrationNumber
                                form.setValue("vehicleNumber", e.target.value);
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="col-span-2">
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      MAKE & MODEL
                    </div>
                    <FormField
                      control={form.control}
                      name="make_model"
                      render={({ field }) => (
                        <FormItem className="mb-2">
                          <FormControl>
                            <Input
                              className="h-8 text-sm border-blue-200"
                              placeholder="Make & Model"
                              {...field}
                              onChange={(e) => {
                                field.onChange(e);
                                // Also update make_model to match make_model
                                form.setValue("make_model", e.target.value);
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="col-span-1">
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      CH. NO.
                    </div>
                    <FormField
                      control={form.control}
                      name="chassisNumber"
                      render={({ field }) => (
                        <FormItem className="mb-2">
                          <FormControl>
                            <Input
                              className="h-8 text-sm border-blue-200"
                              placeholder="Chassis Number"
                              {...field}
                              onChange={(e) => {
                                field.onChange(e);
                                // Also update chassisNumber to match chassisNumber
                                form.setValue("chassisNumber", e.target.value);
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="col-span-1">
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      ENG. NO.
                    </div>
                    <FormField
                      control={form.control}
                      name="engineNumber"
                      render={({ field }) => (
                        <FormItem className="mb-2">
                          <FormControl>
                            <Input
                              className="h-8 text-sm border-blue-200"
                              placeholder="Engine Number"
                              {...field}
                              onChange={(e) => {
                                field.onChange(e);
                                // Also update engineNumber to match engineNumber
                                form.setValue("engineNumber", e.target.value);
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Middle column */}
              <div className="col-span-1 border border-blue-700 rounded p-3">
                <div className="flex flex-col h-full justify-between">
                  <div>
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      MILEAGE
                    </div>
                    <FormField
                      control={form.control}
                      name="mileage"
                      render={({ field }) => (
                        <FormItem className="mb-4">
                          <FormControl>
                            <Input
                              className="h-8 text-sm border-blue-200"
                              placeholder="Vehicle Mileage"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Centre logo */}
                  <div className="flex justify-center items-center">
                    <MRSLogo />
                  </div>
                </div>
              </div>

              {/* Right column */}
              <div className="col-span-1 border border-blue-700 rounded p-3">
                <div className="grid grid-cols-1 gap-2">
                  <div>
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      ESTIMATER:-{" "}
                    </div>
                    <p className="mb-7"></p>
                  </div>

                  {/* <div>
                    <div className="font-bold text-blue-700 mb-1 text-sm">ESTIMATE NO.</div>
                    <div className="text-sm mb-2">EST-{new Date().getFullYear()}-{String(Math.floor(Math.random() * 1000)).padStart(3, '0')}</div>
                  </div> */}

                  <div>
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      OWNER NAME
                    </div>
                    <FormField
                      control={form.control}
                      name="ownerName"
                      render={({ field }) => (
                        <FormItem className="mb-2">
                          <FormControl>
                            <Input
                              className="h-8 text-sm border-blue-200"
                              placeholder="Owner Name"
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div>
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      CUST.TEL NO.
                    </div>
                    <FormField
                      control={form.control}
                      name="customerPhone"
                      render={({ field }) => (
                        <FormItem className="mb-2">
                          <FormControl>
                            <Input
                              className="h-8 text-sm border-blue-200"
                              placeholder="Customer Phone"
                              type="number"
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div>
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      VEH. REG. CARD
                    </div>
                    <div className="flex gap-4 text-sm mb-2">
                      <div className="flex items-center">
                        <input
                          type="radio"
                          id="reg-yes"
                          name="reg-card"
                          className="mr-1"
                          onChange={(e) => {
                            form.setValue("isRegCard", e.target.checked);
                          }}
                        />
                        <label htmlFor="reg-yes" className="text-xs">
                          YES
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="radio"
                          id="reg-no"
                          name="reg-card"
                          className="mr-1"
                          onChange={(e) => {
                            form.setValue("isRegCard", !e.target.checked);
                          }}
                        />
                        <label htmlFor="reg-no" className="text-xs">
                          NO
                        </label>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="font-bold text-blue-700 mb-1 text-sm">
                      E-MAIL
                    </div>
                    <FormField
                      control={form.control}
                      name="customerEmail"
                      render={({ field }) => (
                        <FormItem className="mb-2">
                          <FormControl>
                            <Input
                              className="h-8 text-sm border-blue-200"
                              placeholder="Customer Email"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Job Description Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="col-span-2 border border-blue-700 rounded p-3">
                <div className="font-bold text-blue-700 mb-2 text-sm">
                  JOB DESCRIPTION:-
                </div>
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea
                          placeholder="Enter a detailed description of the required work"
                          className="resize-none min-h-[80px] border-blue-200"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="col-span-1 border border-blue-700 rounded p-3">
                <div className="font-bold text-blue-700 mb-2 text-sm text-center">
                  BODY DAMAGE REPORT
                </div>
                <VehicleDiagram
                  selectedDamages={selectedDamages}
                  onToggleDamage={handleToggleDamage}
                  onAttach={handleAttachDamage}
                />
              </div>
            </div>

            {/* Remarks Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="col-span-2 border border-blue-700 rounded p-3">
                <div className="font-bold text-blue-700 mb-2 text-sm">
                  REMARKS:-
                </div>
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea
                          placeholder="Enter any additional remarks or notes"
                          className="resize-none min-h-[60px] border-blue-200"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="col-span-1 flex flex-col justify-between border border-blue-700 rounded p-3">
                <div className="text-xs mb-2 text-center">
                  I/We authorize MRS Auto Maintenance LLC to carry out the above
                  mentioned jobs. All deliveries subject to availability of
                  spare parts.
                </div>
                <div className="flex flex-col">
                  <div className="font-bold text-blue-700 mb-1 text-sm">
                    Customer Sign.
                  </div>
                  <div className="h-12 border-b border-gray-300 mb-2"></div>
                </div>
              </div>
            </div>

            {/* Acknowledgment Section */}
            <div className="border-t-2 border-dashed border-blue-700 pt-4 mt-4">
              <div className="text-center text-blue-700 font-bold text-lg mb-4">
                MRS AUTO MAINTENANCE LLC
              </div>
              <div className="text-center text-blue-700 font-bold mb-2">
                ACKNOWLEDGMENT
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="col-span-1 border border-blue-700 rounded p-3">
                  <div className="grid grid-cols-1 gap-2">
                    {/* <div> */}
                      {/* <div className="font-bold text-blue-700 mb-1 text-sm">
                        ACKNOWLEDGMENT NO.
                      </div>
                      <div className="text-sm text-red-600 font-bold mb-2">
                        {form.getValues().id ? form.getValues().id : "NEW"}
                      </div> */}
                    {/* </div> */}

                    <div>
                      <div className="font-bold text-blue-700 mb-1 text-sm">
                        CUST.NAME
                      </div>
                      <div className="h-8 text-sm border border-blue-200 rounded px-3 py-1 bg-gray-50">
                        {form.watch("customerName") || ""}
                      </div>
                    </div>

                    <div>
                      <div className="font-bold text-blue-700 mb-1 text-sm">
                        VEH. NO.
                      </div>
                      <div className="h-8 text-sm border border-blue-200 rounded px-3 py-1 bg-gray-50">
                        {form.watch("vehicleNumber") || ""}
                      </div>
                    </div>

                    <div>
                      <div className="font-bold text-blue-700 mb-1 text-sm">
                        RECEIVED BY
                      </div>
                      <div className="h-8 text-sm border border-blue-200 rounded px-3 py-1 bg-gray-50">
                        
                      </div>
                    </div>

                    <div>
                      <div className="font-bold text-blue-700 mb-1 text-sm">
                        CUST.SIGN
                      </div>
                      <div className="h-8 border-b border-gray-300 mb-2"></div>
                    </div>
                  </div>
                </div>

                <div className="col-span-1 flex justify-center items-center">
                  <MRSLogo />
                </div>

                <div className="col-span-1 border border-blue-700 rounded p-3">
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <div className="font-bold text-blue-700 mb-1 text-sm">
                        DATE
                      </div>
                      <div className="text-sm mb-2">
                        {new Date().toLocaleDateString()}
                      </div>
                    </div>

                    <div>
                      <div className="font-bold text-blue-700 mb-1 text-sm">
                        INSURANCE
                      </div>
                      <div className="h-8 text-sm border border-blue-200 rounded px-3 py-1 bg-gray-50">
                        
                      </div>
                    </div>

                    <div>
                      <div className="font-bold text-blue-700 mb-1 text-sm">
                        MAKE & MODEL
                      </div>
                      <FormField
                        disabled={true}
                        control={form.control}
                        name="make_model"
                        render={({ field }) => (
                          <FormItem className="mb-2">
                            <FormControl>
                              <Input
                                className="h-8 text-sm border-blue-200"
                                placeholder="Make & Model"
                                {...field}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div>
                      <div className="font-bold text-blue-700 mb-1 text-sm">
                        PROMISED DEL DATE
                      </div>
                      <FormField
                        control={form.control}
                        name="estimatedCompletionDate"
                        render={({ field }) => (
                          <FormItem className="mb-2">
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant={"outline"}
                                    className={`w-full pl-3 text-left font-normal ${
                                      !field.value && "text-muted-foreground"
                                    }`}
                                  >
                                    {field.value ? (
                                      new Date(field.value).toLocaleDateString()
                                    ) : (
                                      <span>Select date</span>
                                    )}
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="ml-auto h-4 w-4 opacity-50"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      stroke="currentColor"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                      />
                                    </svg>
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <Calendar
                                  mode="single"
                                  selected={
                                    field.value
                                      ? new Date(field.value)
                                      : undefined
                                  }
                                  onSelect={field.onChange}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div>
                      <div className="font-bold text-blue-700 mb-1 text-sm">
                        TEL NO.
                      </div>
                      <div className="text-sm mb-2">
                        {form.getValues("phone") || ""}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-xs text-center border-t border-gray-300 pt-2 mb-2">
                Working Hours 8:00 AM to 6:30 PM, Lunch Time 1:00 PM to 2:00 PM
                <br />
                Remove all your valuables before you leave your vehicle, as we
                will not be responsible for loss/damage
              </div>
            </div>
          </div>

          {/* Footer - only visible in edit mode not in print */}
          <div className="p-4 border-t border-gray-200 flex justify-between print:hidden">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate("/gate-pass")}
            >
              Cancel
            </Button>
            <div className="flex gap-2">
              <Button
                type="submit"
                disabled={isPending}
                className="flex items-center"
              >
                {isPending ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Saving...
                  </>
                ) : (
                  <>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                      />
                    </svg>
                    Save & Print
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Form>
  );
};
