import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON>Footer, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useLocation } from "wouter";
import { useQueryClient } from "@tanstack/react-query";

export const SimpleJobCardForm = () => {
  const { toast } = useToast();
  const [navigate] = useLocation();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    vehicleId: 1, // Fixed to first vehicle for testing
    customerId: 1, // Fixed to first customer for testing
    description: "",
    status: "PENDING"
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      console.log("Submitting job card with data:", formData);
      
      const response = await fetch("/api/jobcards", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create job card");
      }

      const result = await response.json();
      console.log("Job card created successfully:", result);

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["/api/jobcards"] });

      // Show success message
      toast({
        title: "Job Card Created Successfully!",
        description: `Job card ${result.jobNumber} has been created and saved to the database.`,
      });

      // Show alert to ensure user sees confirmation
      alert(`Job Card ${result.jobNumber} has been successfully created and saved. Click OK to return to job cards list.`);

      // Navigate back to job cards list
      navigate("/jobcards");
    } catch (error) {
      console.error("Error creating job card:", error);
      
      toast({
        title: "Error Creating Job Card",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Create New Job Card</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="vehicleId">Vehicle ID</Label>
                <Input
                  id="vehicleId"
                  name="vehicleId"
                  value={formData.vehicleId}
                  onChange={handleChange}
                  disabled
                />
                <p className="text-xs text-gray-500 mt-1">Vehicle: Toyota Camry (Fixed for demo)</p>
              </div>
              <div>
                <Label htmlFor="customerId">Customer ID</Label>
                <Input
                  id="customerId"
                  name="customerId"
                  value={formData.customerId}
                  onChange={handleChange}
                  disabled
                />
                <p className="text-xs text-gray-500 mt-1">Customer: Hiroshi Tanaka (Fixed for demo)</p>
              </div>
            </div>
            <div>
              <Label htmlFor="description">Job Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Enter detailed job description"
                required
                rows={5}
              />
              <p className="text-xs text-gray-500 mt-1">Provide a clear description of the service required</p>
            </div>
          </div>
          <div className="mt-6 flex justify-end">
            <Button
              type="submit"
              className="bg-primary hover:bg-primary/90"
              disabled={isSubmitting || !formData.description.trim()}
            >
              {isSubmitting ? "Creating..." : "Create Job Card"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default SimpleJobCardForm;