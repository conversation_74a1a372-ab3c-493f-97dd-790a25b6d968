import { createContext, ReactN<PERSON>, useContext, useState, useEffect } from "react";
import {
  useQuery,
  useMutation,
  UseMutationResult,
} from "@tanstack/react-query";
import { apiRequest, queryClient } from "../lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { fetchAPI } from "@/lib/api";
import { l } from "node_modules/vite/dist/node/types.d-aGj9QkWt";

type User = {
  id: number;
  username: string;
  name: string;
  role: string;
};

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  loginMutation: UseMutationResult<User, Error, LoginData>;
  logoutMutation: UseMutationResult<void, Error, void>;
};

type LoginData = {
  username: string;
  password: string;
};

// Create context
export const AuthContext = createContext<AuthContextType | null>(null);

// Auth provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Check if user is already logged in on mount
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (e) {
        console.error('Error parsing stored user data', e);
        localStorage.removeItem('user');
      }
    }
    setIsLoading(false);
  }, []);

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginData): Promise<User> => {
      // For this simplified version, we're checking credentials locally
      // In a real app, this would be an API call to the server
      if (credentials) {
        const { username, password } = credentials;

        // api call
        if (!username || !password) {
          throw new Error('Username and password are required');
        }
        const response = await fetchAPI('/login', {
          method: 'POST',
          body: JSON.stringify({ username, password }),
        });   

        console.log('Login response:', response);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Login failed');
        }

        const data = await response.json();
        if (data.token) {
          localStorage.setItem('token', data.token);  
        }
        else {
          throw new Error('Login failed: No token received');
        }


        // Temporay user data
        const user: User = {
          id: 1,
          username: 'mrsauto',
          name: 'MRS Auto Administrator',
          role: 'admin'
        };
        
        // Store user data in localStorage
        localStorage.setItem('user', JSON.stringify(user));
        return user;
      } else {
        throw new Error('Invalid username or password');
      }
    },
    onSuccess: (userData: User) => {
      setUser(userData);
      toast({
        title: "Login successful",
        description: `Welcome back, ${userData.name}`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Login failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      // Clear user data from localStorage
      localStorage.removeItem('user');
    },
    onSuccess: () => {
      setUser(null);
      // Clear any user-specific query data
      queryClient.clear();
      toast({
        title: "Logged out",
        description: "You have been logged out successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Logout failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        error,
        loginMutation,
        logoutMutation,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}