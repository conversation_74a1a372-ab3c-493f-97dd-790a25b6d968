import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useParams, useLocation } from "wouter";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { formatCurrency } from "@/lib/utils";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { 
  FileText, 
  Mail, 
  Printer, 
  Download, 
  Send, 
  Phone,
  MessageCircle 
} from "lucide-react";


const Invoice = () => {
  const params = useParams();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [sendDialogOpen, setSendDialogOpen] = useState(false);
  const [sendMethod, setSendMethod] = useState<'email' | 'whatsapp'>('email');
  const [emailAddress, setEmailAddress] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [customerApprovalDialog, setCustomerApprovalDialog] = useState(false);
  const [customerNote, setCustomerNote] = useState("");
  const [approvalStatus, setApprovalStatus] = useState<'pending' | 'approved' | 'modified'>('pending');

  // Check if we're generating a new invoice from a job card or viewing an existing invoice
  const isGeneratingMode = window.location.pathname.includes('/invoices/generate/');
  const jobCardId = isGeneratingMode && params.jobCardId ? parseInt(params.jobCardId) : 0;
  const invoiceId = !isGeneratingMode && params.id ? parseInt(params.id) : 0;

  // Pre-declare customer variable to avoid LSP error
  const [customerInfo, setCustomerInfo] = useState<any>(null);

  // For existing invoice view - get the complete invoice with all related data
  const { data: invoice, isLoading: invoiceLoading } = useQuery<any>({
    queryKey: ["/api/invoices", invoiceId],
    queryFn: async () => {
      console.log("Fetching invoice with ID:", invoiceId);
      const response = await fetch(`/api/invoices/${invoiceId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch invoice");
      }
      const data = await response.json();
      console.log("Invoice data received:", data);
      
      // Update customer info as soon as we have it
      if (data && data.customer) {
        setCustomerInfo(data.customer);
      }
      
      return data;
    },
    enabled: !isGeneratingMode && !!invoiceId,
    retry: 2
  });

  // For invoice generation mode - this will create a new invoice
  const createInvoiceMutation = useMutation({
    mutationFn: async (data: { jobCardId: number }) => {
      const response = await apiRequest("POST", `/api/invoices`, data);
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Invoice Generated",
        description: "The invoice has been generated successfully.",
      });
      setIsGenerating(false);
      // Navigate to the new invoice using the correct invoice ID from the response
      if (data && data.invoice && data.invoice.id) {
        navigate(`/invoices/${data.invoice.id}`);
      } else if (data && data.id) {
        navigate(`/invoices/${data.id}`);
      } else {
        console.error("Invalid invoice data returned from API:", data);
        toast({
          title: "Error",
          description: "Could not retrieve invoice ID, please try again",
          variant: "destructive",
        });
      }
    },
    onError: () => {
      toast({
        title: "Failed to generate invoice",
        description: "There was an error generating the invoice. Please try again.",
        variant: "destructive",
      });
      setIsGenerating(false);
    }
  });

  // If we're in generate mode, create a new invoice when the component loads
  useEffect(() => {
    if (isGeneratingMode && jobCardId && !isGenerating) {
      setIsGenerating(true);
      createInvoiceMutation.mutate({ jobCardId });
    }
  }, [isGeneratingMode, jobCardId, createInvoiceMutation]);

  // Get job card data for generation mode
  const { data: generationJobCard, isLoading: jobCardLoading } = useQuery<any>({
    queryKey: ["/api/jobcards", jobCardId],
    enabled: isGeneratingMode && !!jobCardId
  });

  // Extract all the data needed from the invoice response
  const jobCard = !isGeneratingMode ? invoice?.jobCard : generationJobCard;
  const customer = !isGeneratingMode ? invoice?.customer : customerInfo;
  const vehicle = !isGeneratingMode ? invoice?.vehicle : null;
  const jobServiceItems = !isGeneratingMode ? invoice?.services || [] : [];
  const jobPartItems = !isGeneratingMode ? invoice?.parts || [] : [];

  // Load customer data in the inputs when it becomes available
  useEffect(() => {
    if (customer) {
      console.log("Setting customer contact info:", customer);
      setEmailAddress(customer.email || '');
      setPhoneNumber(customer.phone || '');
    }
  }, [customer, customerInfo]);

  // Email invoice mutation
  const sendEmailMutation = useMutation({
    mutationFn: async (data: { invoiceId: number, email: string, subject?: string, message?: string }) => {
      const response = await apiRequest("POST", `/api/invoices/${data.invoiceId}/send-email`, { 
        email: data.email,
        subject: data.subject,
        message: data.message
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Invoice Sent",
        description: `Invoice has been sent to ${emailAddress} successfully.`,
      });
      setSendDialogOpen(false);
    },
    onError: () => {
      toast({
        title: "Failed to send invoice",
        description: "There was an error sending the invoice. Please try again.",
        variant: "destructive",
      });
    }
  });

  // WhatsApp invoice mutation
  const sendWhatsAppMutation = useMutation({
    mutationFn: async (data: { invoiceId: number, phoneNumber: string, message?: string }) => {
      const response = await apiRequest("POST", `/api/invoices/${data.invoiceId}/send-whatsapp`, { 
        phoneNumber: data.phoneNumber,
        message: data.message
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Invoice Sent",
        description: `Invoice has been sent to WhatsApp number ${phoneNumber} successfully.`,
      });
      setSendDialogOpen(false);
    },
    onError: () => {
      toast({
        title: "Failed to send invoice",
        description: "There was an error sending the invoice to WhatsApp. Please try again.",
        variant: "destructive",
      });
    }
  });

  const handleSendInvoice = () => {
    if (sendMethod === 'email') {
      if (!emailAddress) {
        toast({
          title: "Email Required",
          description: "Please enter an email address.",
          variant: "destructive",
        });
        return;
      }
      
      // Add approval link to the email message
      const approvalMessage = `
Dear ${customer?.name},

MRS Automaintenance LLP has prepared an invoice for your recent service.
Invoice #: ${invoice?.invoiceNumber}
Total Amount: AED ${total.toFixed(2)}

Please review the invoice details and approve or request modifications.
You can also contact us directly at +971 4 123 4567 if you have any questions.

Thank you for choosing MRS Automaintenance LLP.
`;
      
      sendEmailMutation.mutate({ 
        invoiceId, 
        email: emailAddress,
        subject: `[ACTION REQUIRED] Invoice #${invoice?.invoiceNumber} for Approval - MRS Automaintenance`,
        message: approvalMessage 
      });
      
      // Update approval status
      setApprovalStatus('pending');
      
    } else {
      if (!phoneNumber) {
        toast({
          title: "Phone Number Required",
          description: "Please enter a phone number.",
          variant: "destructive",
        });
        return;
      }
      
      // Add approval message for WhatsApp
      const whatsAppMessage = `
Dear ${customer?.name},

MRS Automaintenance LLP has prepared an invoice #${invoice?.invoiceNumber} for your recent service, totaling AED ${total.toFixed(2)}.

Please review the attached invoice and respond with your approval or any requested modifications.

Thank you for choosing MRS Automaintenance LLP.
`;
      
      sendWhatsAppMutation.mutate({ 
        invoiceId, 
        phoneNumber,
        message: whatsAppMessage
      });
      
      // Update approval status
      setApprovalStatus('pending');
    }
    
    toast({
      title: "Invoice Sent for Approval",
      description: "The customer will be notified to review and approve the invoice.",
    });
  };

  // Show loading state
  if (isGenerating || invoiceLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen gap-2">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        <p>{isGenerating ? "Generating invoice..." : "Loading invoice..."}</p>
      </div>
    );
  }
  
  console.log("Invoice data:", invoice);

  // Check if we have valid data
  if (!isGeneratingMode && !invoice) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-xl font-semibold text-red-600">Invoice not found</p>
        <p className="mt-2 text-gray-600">The requested invoice could not be found.</p>
        <Button 
          onClick={() => navigate('/billing')}
          className="mt-4"
          variant="outline"
        >
          Return to Billing
        </Button>
      </div>
    );
  }

  if (!jobCard) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-xl font-semibold text-red-600">Job Card not found</p>
        <p className="mt-2 text-gray-600">The associated job card could not be found.</p>
        <Button 
          onClick={() => navigate('/jobcards')}
          className="mt-4"
          variant="outline"
        >
          Return to Job Cards
        </Button>
      </div>
    );
  }

  // Calculate totals
  const subtotal = [
    ...jobServiceItems.map((item: any) => item.price * item.quantity),
    ...jobPartItems.map((item: any) => item.price * item.quantity)
  ].reduce((sum, item) => sum + item, 0);
  
  const taxRate = 0.05; // 5% VAT
  const taxAmount = subtotal * taxRate;
  const total = subtotal + taxAmount;

  return (
    <div className="py-6 print-container">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Invoice #{isGeneratingMode ? "New Invoice" : invoice?.invoiceNumber}</h1>
            <p className="text-gray-600">
              {invoice?.createdAt ? format(new Date(invoice.createdAt), 'PPP') : format(new Date(), 'PPP')}
            </p>
          </div>
          <div className="flex space-x-2 print-hidden">
            <Button onClick={() => window.print()} variant="outline" className="flex items-center gap-1">
              <Printer className="h-4 w-4" /> Print
            </Button>

            {/* Direct Email Button */}
            <Button 
              variant="outline"
              className="flex items-center gap-1 bg-blue-50 text-blue-700 hover:bg-blue-100"
              onClick={() => {
                setSendMethod('email');
                setSendDialogOpen(true);
              }}
            >
              <Mail className="h-4 w-4" /> Email
            </Button>
            
            {/* Direct WhatsApp Button */}
            <Button 
              variant="outline"
              className="flex items-center gap-1 bg-green-50 text-green-700 hover:bg-green-100"
              onClick={() => {
                setSendMethod('whatsapp');
                setSendDialogOpen(true);
              }}
            >
              <MessageCircle className="h-4 w-4" /> WhatsApp
            </Button>
            
            <Button 
              variant={approvalStatus === 'approved' ? 'default' : 'outline'} 
              className={approvalStatus === 'approved' ? 'bg-green-100 text-green-800 hover:bg-green-200' : ''}
              disabled
            >
              <FileText className="h-4 w-4 mr-1" />
              {approvalStatus === 'approved' ? 'Approved' : 
               approvalStatus === 'modified' ? 'Modifications Requested' : 
               'Pending Approval'}
            </Button>
            
            <Dialog open={sendDialogOpen} onOpenChange={setSendDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-1">
                  <Send className="h-4 w-4 mr-1" />
                  Send for Approval
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Send Invoice for Approval</DialogTitle>
                  <DialogDescription>
                    Send this invoice to the customer for review and approval.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4 mb-4">
                  <div className="space-y-1">
                    <Label htmlFor="invoice-description">Description</Label>
                    <Textarea 
                      id="invoice-description" 
                      placeholder="Vehicle service and maintenance"
                      className="h-20"
                      defaultValue={jobCard?.description || `Vehicle service for ${customer?.name}'s ${vehicle?.make} ${vehicle?.model} (${vehicle?.registrationNumber})`}
                    />
                    <p className="text-xs text-gray-500">Enter a brief description of the service provided</p>
                  </div>
                  
                  <div className="space-y-1">
                    <Label htmlFor="invoice-remarks">Remarks</Label>
                    <Textarea 
                      id="invoice-remarks" 
                      placeholder="Thank you for your business!"
                      className="h-20"
                      defaultValue={jobCard?.notes || `Thank you for choosing MRS Automaintenance LLP for your vehicle service needs.`}
                    />
                    <p className="text-xs text-gray-500">Add any notes or comments to appear on the invoice</p>
                  </div>
                </div>

                <Tabs defaultValue="email" onValueChange={(value) => setSendMethod(value as 'email' | 'whatsapp')}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="email" className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      <span>Email</span>
                    </TabsTrigger>
                    <TabsTrigger value="whatsapp" className="flex items-center gap-2">
                      <MessageCircle className="h-4 w-4 text-green-600" />
                      <span>WhatsApp</span>
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="email" className="space-y-4 pt-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-blue-500" />
                        <Label htmlFor="email">Email Address</Label>
                      </div>
                      <div className="flex gap-2">
                        <Input 
                          id="email" 
                          type="email" 
                          value={emailAddress || (customer?.email || '')} 
                          onChange={(e) => setEmailAddress(e.target.value)}
                          placeholder="<EMAIL>" 
                          className="flex-1"
                        />
                        <Button
                          type="button"
                          variant="outline" 
                          className="flex items-center gap-1 bg-blue-50 text-blue-700 hover:bg-blue-100 whitespace-nowrap"
                          onClick={() => {
                            if (!emailAddress) {
                              toast({
                                title: "Email Required",
                                description: "Please enter an email address to send the invoice.",
                                variant: "destructive",
                              });
                              return;
                            }
                            
                            // Use existing send function
                            sendEmailMutation.mutate({ 
                              invoiceId, 
                              email: emailAddress,
                              subject: `Invoice #${invoice?.invoiceNumber} - MRS Automaintenance`,
                              message: `
Please find attached your invoice for vehicle service. 
Invoice #: ${invoice?.invoiceNumber}
Total Amount: AED ${total.toFixed(2)}

Thank you for choosing MRS Automaintenance LLP.
` 
                            });
                          }}
                          disabled={sendEmailMutation.isPending || !emailAddress}
                        >
                          <Send className="h-4 w-4" /> Send
                        </Button>
                      </div>
                    </div>
                  </TabsContent>
                  <TabsContent value="whatsapp" className="space-y-4 pt-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <MessageCircle className="h-4 w-4 text-green-600" />
                        <Label htmlFor="phone">WhatsApp Number</Label>
                      </div>
                      <div className="flex gap-2">
                        <Input 
                          id="phone" 
                          type="tel" 
                          value={phoneNumber || (customer?.phone || '')} 
                          onChange={(e) => setPhoneNumber(e.target.value)}
                          placeholder="+971501234567" 
                          className="flex-1"
                        />
                        <Button
                          type="button"
                          variant="outline" 
                          className="flex items-center gap-1 bg-green-50 text-green-700 hover:bg-green-100 whitespace-nowrap"
                          onClick={() => {
                            if (!phoneNumber) {
                              toast({
                                title: "Phone Number Required",
                                description: "Please enter a phone number to send the WhatsApp message.",
                                variant: "destructive",
                              });
                              return;
                            }
                            
                            // Use existing send function
                            sendWhatsAppMutation.mutate({ 
                              invoiceId, 
                              phoneNumber,
                              message: `MRS Auto: Your invoice #${invoice?.invoiceNumber} for AED ${total.toFixed(2)} is ready. Thank you for your business.`
                            });
                          }}
                          disabled={sendWhatsAppMutation.isPending || !phoneNumber}
                        >
                          <Send className="h-4 w-4" /> Send
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500">Enter the full number with country code (e.g., +971)</p>
                    </div>
                  </TabsContent>
                </Tabs>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setSendDialogOpen(false)}>Cancel</Button>
                  <Button 
                    onClick={handleSendInvoice} 
                    disabled={sendEmailMutation.isPending || sendWhatsAppMutation.isPending}
                  >
                    {sendEmailMutation.isPending || sendWhatsAppMutation.isPending ? 'Sending...' : 'Send'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pb-6">
              <div>
                <div className="flex items-center gap-3 mb-3">
                  <img 
                    src="/logo.svg" 
                    alt="MRS Automaintenance LLP" 
                    className="w-16 h-16 object-contain" 
                  />
                  <h3 className="font-bold text-lg">MRS Automaintenance LLP</h3>
                </div>
                <div className="mt-2">
                  <p>123 Garage Street, Dubai</p>
                  <p>United Arab Emirates</p>
                  <p>Phone: +971 4 123 4567</p>
                  <p>Email: <EMAIL></p>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold text-gray-700">Bill To:</h3>
                <div className="mt-2">
                  <p className="font-bold">{customer?.name || 'Customer'}</p>
                  <p>{customer?.address || 'No address'}</p>
                  <p>Phone: {customer?.phone || 'No phone'}</p>
                  <p>Email: {customer?.email || 'No email'}</p>
                  {vehicle && (
                    <div className="mt-2">
                      <p className="font-semibold text-gray-700">Vehicle:</p>
                      <p>{vehicle.make} {vehicle.model} ({vehicle.registrationNumber})</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <Separator className="my-4" />
            
            <div className="mt-6">
              <h3 className="font-semibold text-gray-700 mb-3">Job Details:</h3>
              <p className="mb-2">Job Card: {jobCard?.jobNumber}</p>
              <p className="mb-4">{jobCard?.description}</p>

              <div className="mt-6 mb-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">Description</TableHead>
                      <TableHead className="text-right">Qty</TableHead>
                      <TableHead className="text-right">Unit Price</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {jobServiceItems && jobServiceItems.length > 0 && (
                      <>
                        <TableRow>
                          <TableCell colSpan={4} className="font-semibold">Services</TableCell>
                        </TableRow>
                        {jobServiceItems.map((item: any) => (
                          <TableRow key={`service-${item.id}`}>
                            <TableCell>{item.name}</TableCell>
                            <TableCell className="text-right">{item.quantity}</TableCell>
                            <TableCell className="text-right">{formatCurrency(item.price)}</TableCell>
                            <TableCell className="text-right">{formatCurrency(item.price * item.quantity)}</TableCell>
                          </TableRow>
                        ))}
                      </>
                    )}
                    
                    {jobPartItems && jobPartItems.length > 0 && (
                      <>
                        <TableRow>
                          <TableCell colSpan={4} className="font-semibold">Parts</TableCell>
                        </TableRow>
                        {jobPartItems.map((item: any) => (
                          <TableRow key={`part-${item.id}`}>
                            <TableCell>{item.name}</TableCell>
                            <TableCell className="text-right">{item.quantity}</TableCell>
                            <TableCell className="text-right">{formatCurrency(item.price)}</TableCell>
                            <TableCell className="text-right">{formatCurrency(item.price * item.quantity)}</TableCell>
                          </TableRow>
                        ))}
                      </>
                    )}
                  </TableBody>
                </Table>
              </div>

              <div className="flex flex-col items-end">
                <div className="w-full max-w-md">
                  <div className="flex justify-between py-2">
                    <span className="font-medium">Subtotal:</span>
                    <span>{formatCurrency(subtotal)}</span>
                  </div>
                  <div className="flex justify-between py-2">
                    <span className="font-medium">Tax (5% VAT):</span>
                    <span>{formatCurrency(taxAmount)}</span>
                  </div>
                  <Separator className="my-2" />
                  <div className="flex justify-between py-2 font-bold text-lg">
                    <span>Total:</span>
                    <span>{formatCurrency(total)}</span>
                  </div>
                </div>
              </div>

              <div className="mt-8 pt-4 border-t border-gray-200">
                <h3 className="font-semibold text-gray-700 mb-2">Notes:</h3>
                <p className="text-gray-600">
                  Thank you for choosing MRS Automaintenance LLP for your vehicle service needs. 
                  Payment is due within 14 days from the invoice date.
                </p>
                
                {invoice.isInsuranceClaim && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-md">
                    <p className="text-blue-700 font-medium">Insurance Claim</p>
                    <p className="text-blue-600 text-sm">This job is covered under an insurance claim.</p>
                  </div>
                )}
                
                {invoice.isWarrantyClaim && (
                  <div className="mt-4 p-3 bg-green-50 rounded-md">
                    <p className="text-green-700 font-medium">Warranty Claim</p>
                    <p className="text-green-600 text-sm">This job is covered under warranty.</p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Invoice;