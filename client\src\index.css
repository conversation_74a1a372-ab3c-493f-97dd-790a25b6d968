@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 206 100% 42%;
    --primary-foreground: 210 40% 98%;

    --secondary: 122 39% 28%;
    --secondary-foreground: 210 40% 98%;

    --accent: 41 100% 50%;
    --accent-foreground: 210 40% 98%;

    --destructive: 14 88% 42%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.3rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222.2 47.4% 11.2%;
    --sidebar-primary: 206 100% 42%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 41 100% 50%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 215 20.2% 65.1%;

    --chart-1: 206 100% 42%;
    --chart-2: 122 39% 28%;
    --chart-3: 41 100% 50%;
    --chart-4: 14 88% 42%;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --primary: 206 100% 42%;
    --primary-foreground: 210 40% 98%;

    --secondary: 122 39% 28%;
    --secondary-foreground: 210 40% 98%;

    --accent: 41 100% 50%;
    --accent-foreground: 210 40% 98%;

    --destructive: 14 88% 42%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --sidebar-background: 224 71% 4%;
    --sidebar-foreground: 213 31% 91%;
    --sidebar-primary: 206 100% 42%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 41 100% 50%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 216 34% 17%;
    --sidebar-ring: 216 34% 17%;

    --chart-1: 206 100% 42%;
    --chart-2: 122 39% 28%;
    --chart-3: 41 100% 50%;
    --chart-4: 14 88% 42%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

.shadow-fluent {
  box-shadow: 0 1.6px 3.6px 0 rgba(0, 0, 0, 0.132), 0 0.3px 0.9px 0 rgba(0, 0, 0, 0.108);
}

/* Print styles */
@media print {
  /* Reset the document for printing */
  @page {
    size: A4;
    margin: 10mm;
  }
  
  /* Completely hide UI elements during print */
  body > *:not(.print-container) {
    display: none !important;
  }
  
  /* Hide all navigation, search, and UI controls */
  .print-hidden,
  nav, 
  header,
  .search-container,
  input[type="search"],
  .search-input,
  .search-box,
  .searchbar,
  *[role="search"],
  .sidebar,
  aside,
  .main-nav,
  footer,
  ::-webkit-scrollbar,
  button,
  [data-radix-scroll-area-viewport],
  [role="navigation"],
  [data-orientation] {
    display: none !important;
  }
  
  /* Only show the print container */
  .print-container {
    display: block !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: auto !important;
    overflow: visible !important;
    background-color: white !important;
    z-index: 9999 !important;
  }
  
  /* Show normally hidden print elements */
  .print-show {
    display: block !important;
  }
  
  /* Reset styling for print */
  * {
    background-color: white !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  
  /* Optimize content for printing */
  .print-container {
    font-size: 11pt !important;
  }
  
  .print-container h1 {
    font-size: 16pt !important;
    margin-bottom: 5mm !important;
  }
  
  .print-container h2 {
    font-size: 14pt !important;
    margin-bottom: 3mm !important;
  }
  
  .print-container h3 {
    font-size: 12pt !important;
    margin-bottom: 2mm !important;
  }
  
  /* Make tables more compact */
  .print-container table {
    font-size: 9pt !important;
    border-collapse: collapse !important;
    width: 100% !important;
  }
  
  .print-container table td,
  .print-container table th {
    padding: 1mm !important;
    border: 0.5pt solid #ddd !important;
  }
  
  /* Ensure customer signature section appears properly */
  .signature-section {
    display: block !important;
    margin-top: 10mm !important;
    page-break-inside: avoid !important;
  }
  
  .signature-line {
    display: block !important;
    border-bottom: 1px solid black !important;
    width: 100% !important;
    height: 0 !important;
    margin-top: 10mm !important;
  }
  
  /* Avoid page breaks in cards */
  .card {
    break-inside: avoid !important;
    page-break-inside: avoid !important;
    margin-bottom: 5mm !important;
    border: 1px solid #ddd !important;
    padding: 3mm !important;
  }
}

/* Force page breaks */
.page-break {
  page-break-after: always;
  break-after: page;
}