import ServiceStats from "@/components/dashboard/ServiceStats";
import ServiceJobsTable from "@/components/dashboard/ServiceJobsTable";
import QuickAccessPanel from "@/components/dashboard/QuickAccessPanel";
import RecentActivityPanel from "@/components/dashboard/RecentActivityPanel";
import FinancialMetrics from "@/components/dashboard/FinancialMetrics";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const Dashboard = () => {
  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Dashboard & Reports</h1>
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <Tabs defaultValue="operations" className="mt-4">
          <TabsList className="mb-6">
            <TabsTrigger value="operations">Operations Dashboard</TabsTrigger>
            <TabsTrigger value="executive">Executive View</TabsTrigger>
          </TabsList>
          
          <TabsContent value="operations" className="space-y-8">
            {/* Service Overview Stats */}
            <ServiceStats />
            
            {/* Service Jobs Table */}
            <ServiceJobsTable />

            {/* Bottom Section with Quick Access and Recent Activity */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
              {/* Quick Access Panel */}
              <div className="lg:col-span-1">
                <QuickAccessPanel />
              </div>
              
              {/* Recent Activity Panel */}
              <div className="lg:col-span-2">
                <RecentActivityPanel />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="executive">
            {/* Financial Metrics Dashboard */}
            <FinancialMetrics />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
