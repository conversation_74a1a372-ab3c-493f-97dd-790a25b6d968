import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import InvoiceForm from "@/components/billing/InvoiceForm";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { SearchIcon, FileText, CreditCard, CheckCircle } from "lucide-react";
import { formatDate, formatCurrency } from "@/lib/utils";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const Billing = () => {
  const [selectedJobId, setSelectedJobId] = useState<number | null>(null);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showInvoiceForm, setShowInvoiceForm] = useState(false);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("CREDIT_CARD");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch all job cards
  const { data: jobCards, isLoading: jobsLoading } = useQuery({
    queryKey: ["/api/jobcards"],
  });

  // Fetch all invoices
  const { data: invoices, isLoading: invoicesLoading } = useQuery({
    queryKey: ["/api/invoices"],
  });

  // Process payment mutation
  const processPaymentMutation = useMutation({
    mutationFn: async (data: { invoiceId: number; paymentMethod: string }) => {
      const response = await apiRequest(
        "PATCH", 
        `/api/invoices/${data.invoiceId}/pay`, 
        { paymentMethod: data.paymentMethod }
      );
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/invoices"] });
      queryClient.invalidateQueries({ queryKey: ["/api/jobcards"] });
      toast({
        title: "Payment processed",
        description: "The payment has been successfully processed",
      });
      setShowPaymentDialog(false);
    },
    onError: (error) => {
      toast({
        title: "Error processing payment",
        description: "There was an error processing the payment. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Filter job cards that are ready for invoicing
  const invoiceReadyJobs = jobCards
    ? jobCards.filter((job: any) => {
        const matchesSearch =
          job.jobNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
          job.description.toLowerCase().includes(searchQuery.toLowerCase());
        
        // Jobs that are ready for invoicing
        const isInvoiceReady = job.status === "READY_FOR_PICKUP" && !job.invoiceGenerated;
        
        return matchesSearch && isInvoiceReady;
      })
    : [];

  // Filter invoices by search query
  const filteredInvoices = invoices
    ? invoices.filter((invoice: any) => {
        return invoice.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase());
      })
    : [];

  const handleGenerateInvoice = (jobId: number) => {
    setSelectedJobId(jobId);
    setShowInvoiceForm(true);
  };

  const handleInvoiceFormComplete = () => {
    setShowInvoiceForm(false);
    setSelectedJobId(null);
  };

  const handleProcessPayment = (invoiceId: number) => {
    setSelectedInvoiceId(invoiceId);
    setShowPaymentDialog(true);
  };

  const confirmPayment = () => {
    if (selectedInvoiceId) {
      processPaymentMutation.mutate({
        invoiceId: selectedInvoiceId,
        paymentMethod,
      });
    }
  };

  const isPending = processPaymentMutation.isPending;

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Billing & Payments</h1>
        <p className="mt-1 text-gray-600">
          Generate invoices and process payments for completed services
        </p>

        <div className="mt-6">
          <Tabs defaultValue="invoicing">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="invoicing">Generate Invoices</TabsTrigger>
              <TabsTrigger value="payments">Process Payments</TabsTrigger>
            </TabsList>

            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
              <div className="relative w-full md:w-96">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <Input
                  placeholder="Search job number or invoice..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <TabsContent value="invoicing">
              <div className="grid grid-cols-1 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Ready for Invoicing</CardTitle>
                    <CardDescription>
                      Jobs that are completed and ready for invoice generation
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {jobsLoading ? (
                      <p className="text-center py-4 text-gray-500">Loading jobs...</p>
                    ) : invoiceReadyJobs.length === 0 ? (
                      <div className="text-center py-8">
                        <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500">No jobs ready for invoicing</p>
                        <p className="text-sm text-gray-400 mt-1">
                          Jobs appear here after quality checks are completed
                        </p>
                      </div>
                    ) : (
                      <ul className="divide-y divide-gray-200">
                        {invoiceReadyJobs.map((job: any) => (
                          <li key={job.id} className="py-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <h3 className="text-base font-medium">{job.jobNumber}</h3>
                                <p className="text-sm text-gray-500 mt-1">
                                  {job.description}
                                </p>
                                <div className="flex items-center mt-2">
                                  <span className="text-xs text-gray-500 mr-4">
                                    Vehicle ID: {job.vehicleId}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    Est. Cost: {formatCurrency(job.estimatedCost || 0)}
                                  </span>
                                </div>
                              </div>
                              <Button
                                className="ml-4"
                                onClick={() => handleGenerateInvoice(job.id)}
                              >
                                Generate Invoice
                              </Button>
                            </div>
                          </li>
                        ))}
                      </ul>
                    )}
                  </CardContent>
                </Card>

                {showInvoiceForm && selectedJobId && (
                  <InvoiceForm
                    jobCardId={selectedJobId}
                    onComplete={handleInvoiceFormComplete}
                  />
                )}
              </div>
            </TabsContent>

            <TabsContent value="payments">
              <Card>
                <CardHeader>
                  <CardTitle>Unpaid Invoices</CardTitle>
                  <CardDescription>
                    Process payments for outstanding invoices
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {invoicesLoading ? (
                    <p className="text-center py-4 text-gray-500">Loading invoices...</p>
                  ) : filteredInvoices.length === 0 ? (
                    <div className="text-center py-8">
                      <CreditCard className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500">No unpaid invoices found</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Invoice #
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Job Card
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Date
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Amount
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" className="relative px-6 py-3">
                              <span className="sr-only">Actions</span>
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {filteredInvoices.map((invoice: any) => (
                            <tr key={invoice.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {invoice.invoiceNumber}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                Job #{invoice.jobCardId}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {formatDate(invoice.createdAt)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatCurrency(invoice.total)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  invoice.status === "PAID" 
                                    ? "bg-green-100 text-green-800" 
                                    : "bg-yellow-100 text-yellow-800"
                                }`}>
                                  {invoice.status}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                {invoice.status === "UNPAID" && (
                                  <Button 
                                    variant="outline" 
                                    className="text-primary hover:bg-primary/10"
                                    onClick={() => handleProcessPayment(invoice.id)}
                                  >
                                    Process Payment
                                  </Button>
                                )}
                                {invoice.status === "PAID" && (
                                  <Button 
                                    variant="outline" 
                                    className="text-gray-500 cursor-default"
                                    disabled
                                  >
                                    Paid
                                  </Button>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Payment Processing Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Process Payment</DialogTitle>
            <DialogDescription>
              Select a payment method to complete the transaction.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Payment Method</label>
                <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CREDIT_CARD">Credit Card</SelectItem>
                    <SelectItem value="DEBIT_CARD">Debit Card</SelectItem>
                    <SelectItem value="CASH">Cash</SelectItem>
                    <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                    <SelectItem value="INSURANCE">Insurance</SelectItem>
                    <SelectItem value="WARRANTY">Warranty</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {paymentMethod === "CREDIT_CARD" || paymentMethod === "DEBIT_CARD" ? (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Card Information</label>
                  <Input placeholder="Card number" />
                  <div className="grid grid-cols-2 gap-2">
                    <Input placeholder="MM/YY" />
                    <Input placeholder="CVC" />
                  </div>
                  <Input placeholder="Cardholder name" />
                </div>
              ) : null}
              
              {paymentMethod === "BANK_TRANSFER" ? (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Reference Number</label>
                  <Input placeholder="Enter reference number" />
                </div>
              ) : null}
              
              {paymentMethod === "CASH" ? (
                <div className="border rounded-md p-3 bg-gray-50">
                  <p className="text-sm text-gray-600">
                    Please collect cash payment from the customer and record the transaction.
                  </p>
                </div>
              ) : null}
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowPaymentDialog(false)}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button 
              onClick={confirmPayment}
              disabled={isPending}
            >
              {isPending ? "Processing..." : "Complete Payment"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Billing;
