import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { calculateTotalCost, formatCurrency } from "@/lib/utils";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, PlusCircle, Trash2 } from "lucide-react";
import { format } from "date-fns";

// Extended form validation schema
const jobCardSchema = z.object({
  vehicleId: z.number({
    required_error: "Please select a vehicle",
  }),
  customerId: z.number({
    required_error: "Customer is required",
  }),
  description: z.string().min(1, "Description is required"),
  technicianId: z.number().optional(),
  estimatedCompletionDate: z.date().optional(),
  notes: z.string().optional(),
  isInsuranceClaim: z.boolean().default(false),
  isWarrantyClaim: z.boolean().default(false),
  initialPhotos: z.array(z.string()).optional(),
  // Status is handled by the backend with a default value of "PENDING"
});

type JobCardFormValues = z.infer<typeof jobCardSchema>;

// API request type for job card creation
type JobCardApiRequest = {
  vehicleId: number;
  customerId: number;
  description: string;
  technicianId?: number;
  estimatedCompletionDate?: string;
  notes?: string;
  isInsuranceClaim: boolean;
  isWarrantyClaim: boolean;
  estimatedCost: number;
  status: string;
  initialPhotos?: string[];
};

// Service item type
type ServiceItem = {
  serviceId: number;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
};

// Part item type
type PartItem = {
  partId: number;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
};

const JobCardForm = () => {
  const { toast } = useToast();
  const [, navigate] = useLocation();
  const [location] = useLocation();
  const queryClient = useQueryClient();
  const [selectedServices, setSelectedServices] = useState<ServiceItem[]>([]);
  const [selectedParts, setSelectedParts] = useState<PartItem[]>([]);
  const [photos, setPhotos] = useState<File[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filteredOptions, setFilteredOptions] = useState<any[]>([]);

  // Parse query params to get vehicleId if available
  const params = new URLSearchParams(location.includes("?") ? location.split("?")[1] : "");
  const vehicleIdParam = params.get("vehicleId");

  // Fetch data with refetch interval to ensure we get the latest data
  const { data: vehicles = [], refetch: refetchVehicles } = useQuery<any[]>({
    queryKey: ["/api/vehicles"],
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    staleTime: 0, // Always consider data stale to force refresh
  });

  const { data: customers = [], refetch: refetchCustomers } = useQuery<any[]>({
    queryKey: ["/api/customers"],
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    staleTime: 0, // Always consider data stale to force refresh
  });

  const { data: technicians = [] } = useQuery<any[]>({
    queryKey: ["/api/technicians"],
  });

  const { data: services = [] } = useQuery<any[]>({
    queryKey: ["/api/services"],
  });

  const { data: parts = [] } = useQuery<any[]>({
    queryKey: ["/api/parts"],
  });

  // Get vehicle details if vehicleId is provided
  const selectedVehicle = vehicleIdParam 
    ? vehicles?.find((v: any) => v.id === parseInt(vehicleIdParam)) 
    : null;

  // Initialize form with default values
  const form = useForm<JobCardFormValues>({
    resolver: zodResolver(jobCardSchema),
    defaultValues: {
      vehicleId: selectedVehicle ? parseInt(vehicleIdParam!) : undefined,
      customerId: selectedVehicle?.customerId,
      description: "",
      technicianId: undefined,
      estimatedCompletionDate: undefined,
      notes: "",
      isInsuranceClaim: false,
      isWarrantyClaim: false,
      initialPhotos: [],
    },
  });

  // Update customer when vehicle changes
  useEffect(() => {
    if (vehicles && vehicleIdParam) {
      const vehicle = vehicles.find((v: any) => v.id === parseInt(vehicleIdParam));
      if (vehicle) {
        form.setValue("customerId", vehicle.customerId);
      }
    }
  }, [vehicles, vehicleIdParam, form]);
  
  // Function to handle vehicle-customer search
  const handleVehicleCustomerSearch = (term: string) => {
    setSearchTerm(term);
    if (!term.trim()) {
      setFilteredOptions([]);
      return;
    }
    
    const searchLower = term.toLowerCase();
    let matches: any[] = [];

    // Search for matching license plates (registration numbers)
    if (vehicles) {
      const vehicleMatches = vehicles.filter((vehicle: any) => 
        vehicle.registrationNumber.toLowerCase().includes(searchLower) ||
        vehicle.make.toLowerCase().includes(searchLower) ||
        vehicle.model.toLowerCase().includes(searchLower)
      );
      
      // Add matching vehicles with customer details
      vehicleMatches.forEach((vehicle: any) => {
        const customer = customers?.find((c: any) => c.id === vehicle.customerId);
        if (customer) {
          matches.push({
            id: `v-${vehicle.id}`,
            type: 'vehicle',
            vehicleId: vehicle.id,
            customerId: customer.id,
            label: `${vehicle.make} ${vehicle.model} (${vehicle.registrationNumber}) - ${customer.name}`
          });
        }
      });
    }
    
    // Search for matching customer names
    if (customers) {
      const customerMatches = customers.filter((customer: any) => 
        customer.name.toLowerCase().includes(searchLower) ||
        (customer.phone && customer.phone.includes(term))
      );
      
      // Add matching customers
      customerMatches.forEach((customer: any) => {
        // Check if this customer is already included in a vehicle match
        const alreadyIncluded = matches.some(match => 
          match.type === 'vehicle' && match.customerId === customer.id
        );
        
        if (!alreadyIncluded) {
          matches.push({
            id: `c-${customer.id}`,
            type: 'customer',
            customerId: customer.id,
            label: `${customer.name} (${customer.phone || 'No phone'})`
          });
        }
      });
    }
    
    setFilteredOptions(matches);
  };

  // Create job card mutation
  const createJobCardMutation = useMutation({
    mutationFn: async (jobCardData: JobCardApiRequest) => {
      // Log the data being sent to server for debugging
      console.log("Sending job card data to server:", jobCardData);
      
      // Make the API request
      const response = await apiRequest("POST", "/api/jobcards", jobCardData);
      return response.json();
    },
  });

  // Add service item mutation
  const addServiceItemMutation = useMutation({
    mutationFn: async (data: { jobCardId: number; serviceId: number; quantity: number; price: number; notes?: string }) => {
      const response = await apiRequest("POST", `/api/jobcards/${data.jobCardId}/services`, data);
      return response.json();
    },
  });

  // Add part item mutation
  const addPartItemMutation = useMutation({
    mutationFn: async (data: { jobCardId: number; partId: number; quantity: number; price: number; notes?: string }) => {
      const response = await apiRequest("POST", `/api/jobcards/${data.jobCardId}/parts`, data);
      return response.json();
    },
  });

  const handleAddService = (value: string) => {
    const serviceId = parseInt(value);
    const service = services.find((s: any) => s.id === serviceId);
    if (service) {
      setSelectedServices([
        ...selectedServices,
        {
          serviceId: service.id,
          name: service.name,
          quantity: 1,
          price: service.price,
          notes: "",
        },
      ]);
    }
  };

  const handleAddPart = (value: string) => {
    const partId = parseInt(value);
    const part = parts.find((p: any) => p.id === partId);
    if (part) {
      setSelectedParts([
        ...selectedParts,
        {
          partId: part.id,
          name: part.name,
          quantity: 1,
          price: part.price,
          notes: "",
        },
      ]);
    }
  };

  const handleRemoveService = (index: number) => {
    const newServices = [...selectedServices];
    newServices.splice(index, 1);
    setSelectedServices(newServices);
  };

  const handleRemovePart = (index: number) => {
    const newParts = [...selectedParts];
    newParts.splice(index, 1);
    setSelectedParts(newParts);
  };

  const handleServiceQuantityChange = (index: number, quantity: number) => {
    const newServices = [...selectedServices];
    newServices[index].quantity = quantity;
    setSelectedServices(newServices);
  };

  const handlePartQuantityChange = (index: number, quantity: number) => {
    const newParts = [...selectedParts];
    newParts[index].quantity = quantity;
    setSelectedParts(newParts);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setPhotos(Array.from(e.target.files));
    }
  };

  // Helper function to convert form values to API request format

  // Convert form values to API request format
  const formToApiRequest = (formData: JobCardFormValues, cost: number): JobCardApiRequest => {
    return {
      vehicleId: Number(formData.vehicleId),
      customerId: Number(formData.customerId),
      description: String(formData.description || "").trim(),
      technicianId: formData.technicianId ? Number(formData.technicianId) : undefined,
      estimatedCompletionDate: formData.estimatedCompletionDate ? formData.estimatedCompletionDate.toISOString() : undefined,
      notes: formData.notes ? String(formData.notes).trim() : undefined,
      isInsuranceClaim: Boolean(formData.isInsuranceClaim),
      isWarrantyClaim: Boolean(formData.isWarrantyClaim),
      estimatedCost: cost,
      status: "PENDING",
      initialPhotos: formData.initialPhotos || []
    };
  };

  const onSubmit = async (data: JobCardFormValues) => {
    try {
      // Add debugging to check form values
      console.log("Form data submitted:", data);
      console.log("Form validation state:", form.formState);
      
      // Check for required fields
      if (!data.vehicleId || !data.customerId || !data.description) {
        console.error("Missing required fields:", {
          vehicleId: data.vehicleId,
          customerId: data.customerId,
          description: data.description
        });
        
        toast({
          title: "Missing required fields",
          description: "Please fill in all required fields (Vehicle, Customer, and Description)",
          variant: "destructive",
        });
        return;
      }
      
      // Verify date type if provided
      if (data.estimatedCompletionDate && !(data.estimatedCompletionDate instanceof Date)) {
        console.error("Invalid date format:", data.estimatedCompletionDate);
        toast({
          title: "Invalid Date Format",
          description: "The estimated completion date is in an invalid format",
          variant: "destructive",
        });
        return;
      }
      
      // Calculate estimated cost
      const totalCost = calculateTotalCost(selectedServices, selectedParts);
      
      // Format data for API request
      const jobCardData = formToApiRequest(data, totalCost);

      // Create job card
      const jobCard = await createJobCardMutation.mutateAsync(jobCardData);

      // Add service items
      for (const service of selectedServices) {
        await addServiceItemMutation.mutateAsync({
          jobCardId: jobCard.id,
          serviceId: service.serviceId,
          quantity: service.quantity,
          price: service.price,
          notes: service.notes,
        });
      }

      // Add part items
      for (const part of selectedParts) {
        await addPartItemMutation.mutateAsync({
          jobCardId: jobCard.id,
          partId: part.partId,
          quantity: part.quantity,
          price: part.price,
          notes: part.notes,
        });
      }

      // Handle photo upload in a real app
      if (photos.length > 0) {
        // This would be implemented with actual file upload
        console.log(`Would upload ${photos.length} photos for job card ${jobCard.id}`);
      }

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["/api/jobcards"] });

      // Show success message
      toast({
        title: "Job card created successfully",
        description: `Job card ${jobCard.jobNumber} has been created`,
      });

      // Navigate to job cards page
      navigate("/jobcards");
    } catch (error) {
      console.error("Error creating job card:", error);
      
      // Get a more user-friendly error message
      let errorMessage = "There was an error creating the job card. Please try again.";
      
      if (error instanceof Error) {
        console.error("Error details:", error.message);
        
        // If it's a validation error, show the specific field issues
        if (error.message.includes("Validation Error")) {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: "Error creating job card",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Vehicle & Customer Information */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Vehicle & Customer Information</CardTitle>
              <CardDescription>
                Select the vehicle and customer for this job card
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search field for vehicle/customer */}
              {!vehicleIdParam && (
                <div className="mb-4">
                  <FormLabel htmlFor="search">Quick Search</FormLabel>
                  <div className="flex space-x-2">
                    <Input
                      id="search"
                      type="text"
                      placeholder="Search by license plate, vehicle make/model, or customer name"
                      value={searchTerm}
                      onChange={(e) => handleVehicleCustomerSearch(e.target.value)}
                      className="flex-1"
                    />
                  </div>
                  
                  {filteredOptions.length > 0 && (
                    <div className="relative mt-1">
                      <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                        {filteredOptions.map((option) => (
                          <li 
                            key={option.id}
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => {
                              if (option.type === 'vehicle') {
                                form.setValue("vehicleId", option.vehicleId);
                                form.setValue("customerId", option.customerId);
                                // Force a form validation to update customer information display
                                form.trigger("vehicleId");
                                form.trigger("customerId");
                              } else {
                                form.setValue("customerId", option.customerId);
                                form.trigger("customerId");
                              }
                              setSearchTerm("");
                              setFilteredOptions([]);
                            }}
                          >
                            {option.label}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    Search by license plate, make/model, or customer name to quickly select vehicle/customer
                  </p>
                </div>
              )}
              
              <FormField
                control={form.control}
                name="vehicleId"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>Vehicle*</FormLabel>
                      <Button 
                        type="button" 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => {
                          refetchVehicles();
                          refetchCustomers();
                          toast({
                            title: "Refreshed vehicle list",
                            description: "Vehicle list has been updated with the latest data",
                          });
                        }}
                      >
                        <svg 
                          xmlns="http://www.w3.org/2000/svg" 
                          className="h-4 w-4 mr-1" 
                          fill="none" 
                          viewBox="0 0 24 24" 
                          stroke="currentColor"
                        >
                          <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth={2} 
                            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
                          />
                        </svg>
                        Refresh
                      </Button>
                    </div>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(parseInt(value));
                        const vehicle = vehicles?.find((v: any) => v.id === parseInt(value));
                        if (vehicle) {
                          form.setValue("customerId", vehicle.customerId);
                          // Force form validation to update customer information display
                          form.trigger("vehicleId");
                          form.trigger("customerId");
                        }
                      }}
                      defaultValue={field.value?.toString()}
                      disabled={!!vehicleIdParam}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select vehicle" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {vehicles?.map((vehicle: any) => (
                          <SelectItem key={vehicle.id} value={vehicle.id.toString()}>
                            {vehicle.make} {vehicle.model} ({vehicle.registrationNumber})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                    <FormDescription className="text-xs">
                      If you've just added a new vehicle, click "Refresh" to see it in the list
                    </FormDescription>
                    
                    {/* Display customer details when a vehicle is selected */}
                    {field.value && form.getValues("customerId") && (
                      <div className="mt-4 p-3 bg-gray-50 rounded-md border border-gray-200">
                        <h4 className="text-sm font-medium mb-2">Customer Information</h4>
                        {customers?.find((c: any) => c.id === form.getValues("customerId")) && (
                          <div className="space-y-1 text-sm">
                            <p><span className="font-medium">Name:</span> {customers.find((c: any) => c.id === form.getValues("customerId"))?.name}</p>
                            <p><span className="font-medium">Email:</span> {customers.find((c: any) => c.id === form.getValues("customerId"))?.email}</p>
                            <p><span className="font-medium">Phone:</span> {customers.find((c: any) => c.id === form.getValues("customerId"))?.phone || "N/A"}</p>
                          </div>
                        )}
                      </div>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="customerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer*</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      defaultValue={field.value?.toString()}
                      disabled={!!form.getValues("vehicleId")}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select customer" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {customers?.map((customer: any) => (
                          <SelectItem key={customer.id} value={customer.id.toString()}>
                            {customer.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Service Description*</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the service or repair needed"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="technicianId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Assigned Technician</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(parseInt(value))}
                        defaultValue={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Assign technician" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {technicians?.map((technician: any) => (
                            <SelectItem key={technician.id} value={technician.id.toString()}>
                              {technician.name} ({technician.specialization})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="estimatedCompletionDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Estimated Completion Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={`w-full pl-3 text-left font-normal ${
                                !field.value && "text-muted-foreground"
                              }`}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date(new Date().setHours(0, 0, 0, 0))
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="pt-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Initial Vehicle Photos
                </label>
                <Input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileChange}
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Upload photos of the vehicle condition at job start
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Coverage Information */}
          <Card>
            <CardHeader>
              <CardTitle>Coverage Information</CardTitle>
              <CardDescription>
                Mark if this job is covered by insurance or warranty
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="isInsuranceClaim"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Insurance Claim</FormLabel>
                      <FormDescription>
                        This service is covered by insurance
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isWarrantyClaim"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Warranty Claim</FormLabel>
                      <FormDescription>
                        This service is covered by warranty
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Any additional notes about the job..."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        </div>

        {/* Service Items & Parts */}
        <Card>
          <CardHeader>
            <CardTitle>Service Items & Parts</CardTitle>
            <CardDescription>
              Add services and parts required for this job
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="services" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="services">Services</TabsTrigger>
                <TabsTrigger value="parts">Parts</TabsTrigger>
              </TabsList>
              <TabsContent value="services" className="pt-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Service Items</h3>
                  <div className="flex items-center gap-2">
                    <Select onValueChange={handleAddService}>
                      <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Add service" />
                      </SelectTrigger>
                      <SelectContent>
                        {services?.map((service: any) => (
                          <SelectItem key={service.id} value={service.id.toString()}>
                            {service.name} ({formatCurrency(service.price)})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      type="button"
                      size="sm"
                      variant="outline"
                      className="flex items-center gap-1"
                      onClick={() => document.querySelector<HTMLButtonElement>('[role="combobox"]')?.click()}
                    >
                      <PlusCircle className="h-4 w-4" />
                      Add
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[300px]">Service</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Price</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedServices.length > 0 ? (
                        selectedServices.map((service, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">
                              {service.name}
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                min="1"
                                value={service.quantity}
                                onChange={(e) =>
                                  handleServiceQuantityChange(
                                    index,
                                    parseInt(e.target.value)
                                  )
                                }
                                className="w-16"
                              />
                            </TableCell>
                            <TableCell>{formatCurrency(service.price)}</TableCell>
                            <TableCell>
                              {formatCurrency(service.price * service.quantity)}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveService(index)}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                            No services added yet
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              <TabsContent value="parts" className="pt-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Parts</h3>
                  <div className="flex items-center gap-2">
                    <Select onValueChange={handleAddPart}>
                      <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Add part" />
                      </SelectTrigger>
                      <SelectContent>
                        {parts?.map((part: any) => (
                          <SelectItem key={part.id} value={part.id.toString()}>
                            {part.name} ({formatCurrency(part.price)})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      type="button"
                      size="sm"
                      variant="outline"
                      className="flex items-center gap-1"
                      onClick={() => document.querySelector<HTMLButtonElement>('[role="combobox"]')?.click()}
                    >
                      <PlusCircle className="h-4 w-4" />
                      Add
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[300px]">Part</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Price</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedParts.length > 0 ? (
                        selectedParts.map((part, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">{part.name}</TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                min="1"
                                value={part.quantity}
                                onChange={(e) =>
                                  handlePartQuantityChange(
                                    index,
                                    parseInt(e.target.value)
                                  )
                                }
                                className="w-16"
                              />
                            </TableCell>
                            <TableCell>{formatCurrency(part.price)}</TableCell>
                            <TableCell>
                              {formatCurrency(part.price * part.quantity)}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemovePart(index)}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                            No parts added yet
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between border-t p-4">
            <div>
              <p className="text-sm text-gray-500">Estimated Total:</p>
              <p className="text-xl font-semibold">
                {formatCurrency(calculateTotalCost(selectedServices, selectedParts))}
              </p>
            </div>
          </CardFooter>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate("/jobcards")}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              createJobCardMutation.isPending ||
              addServiceItemMutation.isPending ||
              addPartItemMutation.isPending
            }
          >
            {createJobCardMutation.isPending
              ? "Creating..."
              : "Create Job Card"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default JobCardForm;
