import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import VehicleForm from "@/components/vehicle/VehicleForm";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Search } from "lucide-react";

const VehicleCheckIn = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("new");

  const { data: vehicles, isLoading } = useQuery({
    queryKey: ["/api/vehicles"],
    enabled: activeTab === "existing",
  });

  const filteredVehicles = vehicles?.filter(
    (vehicle: any) =>
      vehicle.registrationNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vehicle.vin.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vehicle.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vehicle.model.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Vehicle Check-In</h1>
        <p className="mt-2 text-gray-600">Register a new vehicle or check-in an existing one</p>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 mt-6">
        <Tabs defaultValue="new" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="new">New Vehicle</TabsTrigger>
            <TabsTrigger value="existing">Existing Vehicle</TabsTrigger>
          </TabsList>
          
          <TabsContent value="new">
            <Card>
              <CardHeader>
                <CardTitle>Register New Vehicle</CardTitle>
                <CardDescription>
                  Enter vehicle and customer details to register a new vehicle in the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <VehicleForm />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="existing">
            <Card>
              <CardHeader>
                <CardTitle>Find Existing Vehicle</CardTitle>
                <CardDescription>
                  Search for a vehicle by registration number, VIN, make or model
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="relative mb-6">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    type="search"
                    placeholder="Search vehicles..."
                    className="pl-10"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                {isLoading ? (
                  <div className="text-center py-4">Loading vehicles...</div>
                ) : filteredVehicles?.length ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Vehicle
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Registration
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            VIN
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Customer
                          </th>
                          <th scope="col" className="relative px-6 py-3">
                            <span className="sr-only">Actions</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredVehicles.map((vehicle: any) => (
                          <tr key={vehicle.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">
                                {vehicle.make} {vehicle.model} ({vehicle.year})
                              </div>
                              <div className="text-sm text-gray-500">
                                {vehicle.color}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {vehicle.registrationNumber}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {vehicle.vin}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              Customer ID: {vehicle.customerId}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <Button asChild variant="link" className="text-primary">
                                <a href={`/job-cards/new?vehicleId=${vehicle.id}`}>Check In</a>
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No vehicles found matching "{searchQuery}"</p>
                    <Button className="mt-4" asChild>
                      <a href="#new">Register a New Vehicle</a>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default VehicleCheckIn;
