// // import { Pool, neonConfig } from '@neondatabase/serverless';
// // import { drizzle } from 'drizzle-orm/neon-serverless';
// // import ws from "ws";


// import { drizzle } from 'drizzle-orm/node-postgres';
// import pg from 'pg';
// const { Pool } = pg;

// import * as schema from "@shared/schema";

// // neonConfig.webSocketConstructor = ws;

// // if (!process.env.DATABASE_URL) {
// //   throw new Error(
// //     "DATABASE_URL must be set. Did you forget to provision a database?",
// //   );
// // }

// // export const pool = new Pool({ connectionString: "postgresql://postgres:1234@localhost:5432/garage_db" });
// // export const db = drizzle({ client: pool, schema });

// export const pool = new Pool({
//     // Your PostgreSQL connection string or options
//     connectionString: process.env.DATABASE_URL || 'postgresql://postgres:1234@localhost:5432/garage_db',
// });
// export const db = drizzle(pool, { schema }); // Pass your schema to Drizzle
// // --- End Drizzle DB setup ---



import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as schema from './schema';

export const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  // Optional: schema search_path if using one schema per garage
  // options: '-c search_path=garage_a'
});

export const db = drizzle(pool, { schema });

