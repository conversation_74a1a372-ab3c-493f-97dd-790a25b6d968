// src/routes/auth.ts
import { Router } from 'express';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { db } from '../db';
import { users } from '../schema';
import { eq } from 'drizzle-orm';

const router = Router();


// check is server is running
    router.get('/', (_req, res) => {    
    res.send('Garage ERP API is running 🚗');
    });



// Register
router.post('/register', async (req, res) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ error: 'Username and password are required' });
  }

  // Check if user already exists
  const [existingUser] = await db.select().from(users).where(eq(users.username, username));
  if (existingUser) {
    return res.status(409).json({ error: 'User already exists' });
  }

  const hash = await bcrypt.hash(password, 10);

  try {
    await db.insert(users).values({ username, password: hash, client: 'mrs' });
    res.status(201).json({ message: 'User created' });
  } catch (err) {
    res.status(400).json({ error: 'Invalid data' });
  }
});

// Login
router.post('/login', async (req, res) => {
    console.log('Login request received:', req.body);
  const { username, password } = req.body;

  // const all_users = await db.select().from(users);
  // console.log('All users in the database:', all_users);

  const [user] = await db.select().from(users).where(eq(users.username, username));
  console.log('User found:', user);
  if (!user) return res.status(401).json({ error: 'Invalid credentials' });

  // const match = await bcrypt.compare(password, user.password);
  // if (!match) return res.status(401).json({ error: 'Invalid credentials' });

  // const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET!, { expiresIn: '1d' });
  const token = 'blablabla'
  res.json({ token });
});

export default router;
