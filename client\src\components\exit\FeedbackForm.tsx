import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

// Form validation schema
const feedbackFormSchema = z.object({
  feedbackRating: z.enum(["1", "2", "3", "4", "5"], {
    required_error: "Please select a rating",
  }),
  feedback: z.string().min(1, "Feedback is required"),
  customerSignature: z.string().min(1, "Signature is required"),
});

type FeedbackFormValues = z.infer<typeof feedbackFormSchema>;

type FeedbackFormProps = {
  jobCardId: number;
  onComplete: () => void;
};

const FeedbackForm = ({ jobCardId, onComplete }: FeedbackFormProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Initialize form with default values
  const form = useForm<FeedbackFormValues>({
    resolver: zodResolver(feedbackFormSchema),
    defaultValues: {
      feedbackRating: undefined,
      feedback: "",
      customerSignature: "",
    },
  });

  // Update job card mutation
  const updateJobCardMutation = useMutation({
    mutationFn: async (data: {
      status: string;
      feedback: string;
      feedbackRating: number;
    }) => {
      const response = await apiRequest(
        "PATCH",
        `/api/jobcards/${jobCardId}`,
        data
      );
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [`/api/jobcards/${jobCardId}`],
      });
      queryClient.invalidateQueries({ queryKey: ["/api/jobcards"] });
      toast({
        title: "Feedback submitted",
        description: "Thank you for your feedback!",
      });
      onComplete();
    },
    onError: (error) => {
      toast({
        title: "Error submitting feedback",
        description:
          "There was an error submitting your feedback. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = async (data: FeedbackFormValues) => {
    try {
      // Update job card with feedback
      await updateJobCardMutation.mutateAsync({
        status: "COMPLETED",
        feedback: data.feedback,
        feedbackRating: parseInt(data.feedbackRating),
      });
    } catch (error) {
      console.error("Error submitting feedback:", error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Service Feedback</CardTitle>
            <CardDescription>
              Please take a moment to provide feedback on your service experience
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="feedbackRating"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>How would you rate your service experience?</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex space-x-1"
                    >
                      {[1, 2, 3, 4, 5].map((rating) => (
                        <div key={rating} className="flex flex-col items-center space-y-1">
                          <RadioGroupItem
                            value={rating.toString()}
                            id={`rating-${rating}`}
                            className="sr-only"
                          />
                          <Label
                            htmlFor={`rating-${rating}`}
                            className={`h-12 w-12 rounded-full flex items-center justify-center text-lg cursor-pointer transition-colors ${
                              field.value === rating.toString()
                                ? "bg-primary text-white"
                                : "bg-gray-100 hover:bg-gray-200 text-gray-800"
                            }`}
                          >
                            {rating}
                          </Label>
                          <span className="text-xs text-gray-500">
                            {rating === 1
                              ? "Poor"
                              : rating === 2
                              ? "Fair"
                              : rating === 3
                              ? "Good"
                              : rating === 4
                              ? "Very Good"
                              : "Excellent"}
                          </span>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="feedback"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Your Feedback</FormLabel>
                  <FormDescription>
                    Please share your thoughts about the service you received
                  </FormDescription>
                  <FormControl>
                    <Textarea
                      placeholder="Tell us about your experience..."
                      className="min-h-[120px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="customerSignature"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Digital Signature</FormLabel>
                  <FormDescription>
                    Please type your full name as your digital signature
                  </FormDescription>
                  <FormControl>
                    <Input placeholder="Your full name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="border-t px-6 py-4 flex justify-end">
            <Button
              type="submit"
              disabled={updateJobCardMutation.isPending}
            >
              {updateJobCardMutation.isPending
                ? "Submitting..."
                : "Submit Feedback"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
};

export default FeedbackForm;
