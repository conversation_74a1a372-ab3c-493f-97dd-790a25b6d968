import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { vehicleMakes } from "@/lib/utils";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";

// Extended form validation schema
const vehicleFormSchema = z.object({
  make: z.string().min(1, "Make is required"),
  model: z.string().min(1, "Model is required"),
  year: z.string().min(4, "Valid year is required").max(4),
  registrationNumber: z.string().min(1, "Registration number is required"),
  vin: z.string().min(1, "VIN is required"),
  color: z.string().optional(),
  // Customer fields
  customerName: z.string().min(1, "Customer name is required"),
  customerEmail: z.string().email("Invalid email address"),
  customerPhone: z.string().min(1, "Phone number is required"),
  customerAddress: z.string().optional(),
  // Insurance and warranty fields
  hasInsurance: z.boolean().default(false),
  insuranceProvider: z.string().optional(),
  insurancePolicy: z.string().optional(),
  hasWarranty: z.boolean().default(false),
  warrantyInfo: z.string().optional(),
});

type VehicleFormValues = z.infer<typeof vehicleFormSchema>;

const VehicleForm = () => {
  const { toast } = useToast();
  const [, navigate] = useLocation();
  const queryClient = useQueryClient();
  const [initialPhotos, setInitialPhotos] = useState<File[]>([]);

  // Initialize form with default values
  const form = useForm<VehicleFormValues>({
    resolver: zodResolver(vehicleFormSchema),
    defaultValues: {
      make: "",
      model: "",
      year: new Date().getFullYear().toString(),
      registrationNumber: "",
      vin: "",
      color: "",
      customerName: "",
      customerEmail: "",
      customerPhone: "",
      customerAddress: "",
      hasInsurance: false,
      insuranceProvider: "",
      insurancePolicy: "",
      hasWarranty: false,
      warrantyInfo: "",
    },
  });

  const { watch, setValue } = form;
  const hasInsurance = watch("hasInsurance");
  const hasWarranty = watch("hasWarranty");

  // Create customer mutation
  const createCustomerMutation = useMutation({
    mutationFn: async (customerData: {
      name: string;
      email: string;
      phone: string;
      address?: string;
    }) => {
      const response = await apiRequest("POST", "/api/customers", customerData);
      return response.json();
    },
  });

  // Create vehicle mutation
  const createVehicleMutation = useMutation({
    mutationFn: async (vehicleData: {
      customerId: number;
      make: string;
      model: string;
      year: number;
      registrationNumber: string;
      vin: string;
      color?: string;
      insuranceProvider?: string;
      insurancePolicy?: string;
      warrantyInfo?: string;
    }) => {
      const response = await apiRequest("POST", "/api/vehicles", vehicleData);
      return response.json();
    },
    onSuccess: () => {
      // Invalidate vehicles query to ensure JobCardForm gets the latest data
      queryClient.invalidateQueries({ queryKey: ["/api/vehicles"] });
    }
  });

  const onSubmit = async (data: VehicleFormValues) => {
    try {
      // First create the customer
      const customer = await createCustomerMutation.mutateAsync({
        name: data.customerName,
        email: data.customerEmail,
        phone: data.customerPhone,
        address: data.customerAddress,
      });

      // Then create the vehicle linked to the customer
      const vehicle = await createVehicleMutation.mutateAsync({
        customerId: customer.id,
        make: data.make,
        model: data.model,
        year: parseInt(data.year),
        registrationNumber: data.registrationNumber,
        vin: data.vin,
        color: data.color,
        insuranceProvider: data.hasInsurance ? data.insuranceProvider : undefined,
        insurancePolicy: data.hasInsurance ? data.insurancePolicy : undefined,
        warrantyInfo: data.hasWarranty ? data.warrantyInfo : undefined,
      });

      // Upload initial photos if any (in a real app)
      if (initialPhotos.length > 0) {
        // This would be implemented with actual file upload
        console.log(`Would upload ${initialPhotos.length} photos for vehicle ${vehicle.id}`);
      }

      // Only need to invalidate customer queries as vehicle queries are invalidated in onSuccess
      queryClient.invalidateQueries({ queryKey: ["/api/customers"] });

      // Show success message
      toast({
        title: "Vehicle registered successfully",
        description: `${data.make} ${data.model} has been registered with customer ${data.customerName}`,
      });

      // Navigate to create a new job card for this vehicle
      navigate(`/job-cards/new?vehicleId=${vehicle.id}`);
    } catch (error) {
      console.error("Error registering vehicle:", error);
      toast({
        title: "Error registering vehicle",
        description: "There was an error registering the vehicle. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setInitialPhotos(Array.from(e.target.files));
    }
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 30 }, (_, i) => (currentYear - i).toString());

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Vehicle Information */}
          <Card>
            <CardContent className="pt-6">
              <h3 className="text-lg font-medium mb-4">Vehicle Information</h3>
              <Separator className="mb-4" />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="make"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Make*</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select make" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {vehicleMakes.map((make) => (
                            <SelectItem key={make} value={make}>
                              {make}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="model"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Model*</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Camry" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4 mt-4">
                <FormField
                  control={form.control}
                  name="year"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Year*</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select year" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {years.map((year) => (
                            <SelectItem key={year} value={year}>
                              {year}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="color"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Color</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Silver" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 mt-4">
                <FormField
                  control={form.control}
                  name="registrationNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Registration Number*</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. ABC-1234" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>VIN (Vehicle Identification Number)*</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. 1HGCM82633A004352" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Initial Vehicle Photos
                </label>
                <Input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileChange}
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Upload photos of the vehicle condition at check-in
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Customer & Coverage Information */}
          <div className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">Customer Information</h3>
                <Separator className="mb-4" />

                <FormField
                  control={form.control}
                  name="customerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer Name*</FormLabel>
                      <FormControl>
                        <Input placeholder="Full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                  <FormField
                    control={form.control}
                    name="customerEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email*</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="customerPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone*</FormLabel>
                        <FormControl>
                          <Input placeholder="(*************" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="mt-4">
                  <FormField
                    control={form.control}
                    name="customerAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Address</FormLabel>
                        <FormControl>
                          <Input placeholder="Street address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">Coverage Information</h3>
                <Separator className="mb-4" />

                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="hasInsurance"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Vehicle has insurance coverage</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />

                  {hasInsurance && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                      <FormField
                        control={form.control}
                        name="insuranceProvider"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Insurance Provider</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="e.g. State Farm"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="insurancePolicy"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Policy Number</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="e.g. POL-12345678"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="hasWarranty"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Vehicle is under warranty</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />

                  {hasWarranty && (
                    <FormField
                      control={form.control}
                      name="warrantyInfo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Warranty Information</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="e.g. Extended warranty until 12/2023"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate("/")}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createCustomerMutation.isPending || createVehicleMutation.isPending}
          >
            {(createCustomerMutation.isPending || createVehicleMutation.isPending) ? "Registering..." : "Register Vehicle"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default VehicleForm;
