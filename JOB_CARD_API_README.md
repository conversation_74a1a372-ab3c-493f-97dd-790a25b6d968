# Job Card Creation API

This document describes the new API endpoint for creating job cards from quotations with complete embedded data.

## Overview

The new job card creation API allows you to create a job card directly from a quotation ID. The job card will contain all necessary data embedded within it, without relying on foreign key references to other tables.

## API Endpoint

### Create Job Card from Quotation

**Endpoint:** `POST /api/gate-pass/job-card/create-from-quotation`

**Description:** Creates a new job card with complete embedded data from an existing quotation.

**Request Body:**
```json
{
  "quotationId": 123
}
```

**Response:**
```json
{
  "message": "Job card created successfully from quotation",
  "jobCard": {
    "id": 456,
    "jobNumber": "JOB-2025-1736267890123",
    "vehicleRegistrationNumber": "DXB-12345",
    "vehicleMake": "Toyota",
    "vehicleModel": "Camry",
    "vehicleColor": "White",
    "vehicleEngineNumber": "ENG123456",
    "vehicleChassisNumber": "CHS789012",
    "customerName": "<PERSON>",
    "ownerName": "<PERSON>",
    "customerEmail": "<EMAIL>",
    "customerPhone": "+971501234567",
    "serviceType": "Maintenance",
    "mileage": "50000",
    "description": "Oil change and brake inspection",
    "jobDescription": "Oil change and brake inspection",
    "partsUsed": "[{\"itemName\":\"Engine Oil Filter\",\"price\":\"25.00\"}]",
    "servicesUsed": "[{\"itemName\":\"Oil Change Service\",\"price\":\"75.00\"}]",
    "tax": "5.0",
    "totalAmount": "315.00",
    "status": "pending",
    "estimatedDeliveryDate": "2025-01-15T10:30:00.000Z",
    "estimatedCompletionDate": "2025-01-15T10:30:00.000Z",
    "notes": "Premium service package",
    "remarks": "Customer requested premium oil",
    "quotationNotes": "Premium service package",
    "quotationDescription": "Complete maintenance service",
    "createdAt": "2025-01-08T10:30:00.000Z",
    "updatedAt": "2025-01-08T10:30:00.000Z"
  },
  "sourceData": {
    "quotationId": 123,
    "gatePassId": 789,
    "vehicleId": 456
  }
}
```

## Job Card Schema

The job card contains the following embedded data:

### Vehicle Information
- `vehicleRegistrationNumber`: Vehicle registration number
- `vehicleMake`: Vehicle make (e.g., Toyota)
- `vehicleModel`: Vehicle model (e.g., Camry)
- `vehicleColor`: Vehicle color
- `vehicleEngineNumber`: Engine number
- `vehicleChassisNumber`: Chassis number

### Customer Information
- `customerName`: Customer name
- `ownerName`: Vehicle owner name
- `customerEmail`: Customer email address
- `customerPhone`: Customer phone number

### Service Information
- `serviceType`: Type of service
- `mileage`: Vehicle mileage
- `description`: Job description
- `jobDescription`: Detailed job description
- `partsUsed`: JSON string of parts used
- `servicesUsed`: JSON string of services used

### Financial Information
- `tax`: Tax percentage
- `totalAmount`: Total amount including tax

### Status and Dates
- `status`: Job status (default: "pending")
- `estimatedDeliveryDate`: Estimated delivery date
- `estimatedCompletionDate`: Estimated completion date

### Additional Information
- `notes`: General notes
- `remarks`: Additional remarks
- `quotationNotes`: Notes from quotation
- `quotationDescription`: Description from quotation

## Error Responses

### 400 Bad Request
```json
{
  "error": "Quotation ID is required"
}
```

### 404 Not Found
```json
{
  "error": "Quotation not found"
}
```

```json
{
  "error": "Associated gate pass not found"
}
```

```json
{
  "error": "Associated vehicle not found"
}
```

### 500 Internal Server Error
```json
{
  "error": "Failed to create job card from quotation",
  "details": "Specific error message"
}
```

## Testing

A test script is provided to verify the API functionality:

```bash
# Make sure the server is running
npm run dev:backend

# In another terminal, run the test
node server/test-job-card-api.js
```

The test script will:
1. Create a test vehicle and gate pass
2. Create a test quotation
3. Create a job card from the quotation
4. Verify all data is correctly embedded
5. Display test results

## Usage Example

```javascript
// Example usage with fetch
const response = await fetch('/api/gate-pass/job-card/create-from-quotation', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    quotationId: 123
  })
});

const result = await response.json();
console.log('Job card created:', result.jobCard.jobNumber);
```

## Benefits

1. **Complete Data Storage**: All necessary information is embedded in the job card
2. **No Foreign Key Dependencies**: Job card can exist independently
3. **Data Integrity**: Complete snapshot of data at the time of job card creation
4. **Performance**: No need for complex joins when retrieving job card data
5. **Audit Trail**: Preserves exact state of quotation, vehicle, and customer data

## Notes

- The job card number is automatically generated with format: `JOB-YYYY-timestamp`
- Total amount is calculated from parts and services with tax applied
- All JSON fields (partsUsed, servicesUsed) are stored as strings and need to be parsed when retrieved
- The API preserves all original data from quotation, gate pass, and vehicle records
