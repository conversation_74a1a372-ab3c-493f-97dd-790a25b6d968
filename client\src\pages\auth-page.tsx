import { useEffect } from "react";
import { useLocation } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, CheckCircle, Clock, Users, Wrench, Settings } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

const loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function AuthPage() {
  const [, navigate] = useLocation();
  const { user, loginMutation } = useAuth();

  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "mrsauto",
      password: "Passw0rd1",
    },
  });

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      navigate("/");
    }
  }, [user, navigate]);

  const onLoginSubmit = (data: LoginFormValues) => {
    loginMutation.mutate(data);
  };

  return (
    <div className="flex min-h-screen bg-muted/10 w-full">
      {/* Left Side - Form */}
      <div className="hidden lg:flex lg:w-1/2 bg-primary flex-col justify-center items-center text-white p-12">
        <div className="max-w-md space-y-6">
          <h1 className="text-4xl font-bold">Complete Garage Management Solution</h1>
          <p className="text-lg opacity-90">
            Streamline your automotive service operations with our comprehensive
            garage management system designed specifically for MRS Automaintenance LLP.
          </p>
          <div className="space-y-4 mt-8">
            <div className="flex items-start space-x-4">
              <Settings className="h-6 w-6 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-semibold">End-to-End Service Tracking</h3>
                <p className="opacity-90">Monitor vehicles from check-in to delivery with detailed documentation</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <Users className="h-6 w-6 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-semibold">Customer Relationship Management</h3>
                <p className="opacity-90">Maintain comprehensive customer and vehicle records for better service</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <Clock className="h-6 w-6 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-semibold">Real-time Job Progress</h3>
                <p className="opacity-90">Track service progress in real-time for improved workflow management</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <CheckCircle className="h-6 w-6 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-semibold">Quality Assurance System</h3>
                <p className="opacity-90">Enforce quality checks at every stage to ensure perfect service delivery</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <Wrench className="h-6 w-6 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-semibold">Inventory & Parts Management</h3>
                <p className="opacity-90">Track parts usage and maintain optimal inventory levels</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Right Side - Hero/Branding */}
      <div className="flex flex-col justify-center items-center w-full lg:w-1/2 p-8">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center flex flex-col items-center">
            {/* Company Name */}
            <h2 className="text-3xl font-bold tracking-tight">MRS Automaintenance LLP</h2>
            <p className="text-sm text-muted-foreground mt-2">
              Garage Management System Login
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Login</CardTitle>
              <CardDescription>
                Enter your credentials to access the garage management system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...loginForm}>
                <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                  <FormField
                    control={loginForm.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Username</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter your username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={loginForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <Input type="password" placeholder="••••••••" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {loginMutation.isError && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Invalid username or password. Please try again.
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={loginMutation.isPending}
                  >
                    {loginMutation.isPending ? "Signing in..." : "Sign in"}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>

    </div>
  );
}