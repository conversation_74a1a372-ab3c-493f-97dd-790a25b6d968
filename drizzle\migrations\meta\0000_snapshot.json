{"id": "42ee0dc5-bb69-48df-bcf3-fc8c650bc1f7", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.gate_pass": {"name": "gate_pass", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "owner_name": {"name": "owner_name", "type": "text", "primaryKey": false, "notNull": true}, "customer_name": {"name": "customer_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "service_type": {"name": "service_type", "type": "text", "primaryKey": false, "notNull": true}, "mileage": {"name": "mileage", "type": "text", "primaryKey": false, "notNull": true}, "estimated_delivery_date": {"name": "estimated_delivery_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "job_description": {"name": "job_description", "type": "text", "primaryKey": false, "notNull": true}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "canvas_json": {"name": "canvas_json", "type": "json", "primaryKey": false, "notNull": false, "default": "'null'::json"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"gate_pass_vehicle_id_vehicles_id_fk": {"name": "gate_pass_vehicle_id_vehicles_id_fk", "tableFrom": "gate_pass", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quotations": {"name": "quotations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "gate_pass_id": {"name": "gate_pass_id", "type": "integer", "primaryKey": false, "notNull": true}, "parts_used": {"name": "parts_used", "type": "json", "primaryKey": false, "notNull": true}, "services_used": {"name": "services_used", "type": "json", "primaryKey": false, "notNull": true}, "tax": {"name": "tax", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"quotations_gate_pass_id_gate_pass_id_fk": {"name": "quotations_gate_pass_id_gate_pass_id_fk", "tableFrom": "quotations", "tableTo": "gate_pass", "columnsFrom": ["gate_pass_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.services": {"name": "services", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_name": {"name": "service_name", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "client": {"name": "client", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_parts": {"name": "vehicle_parts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "item_name": {"name": "item_name", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicles": {"name": "vehicles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "vehicle_registration_number": {"name": "vehicle_registration_number", "type": "text", "primaryKey": false, "notNull": true}, "make": {"name": "make", "type": "text", "primaryKey": false, "notNull": true}, "model_model": {"name": "model_model", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true}, "engine_number": {"name": "engine_number", "type": "text", "primaryKey": false, "notNull": true}, "chassis_number": {"name": "chassis_number", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"vehicles_vehicle_registration_number_unique": {"name": "vehicles_vehicle_registration_number_unique", "nullsNotDistinct": false, "columns": ["vehicle_registration_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}