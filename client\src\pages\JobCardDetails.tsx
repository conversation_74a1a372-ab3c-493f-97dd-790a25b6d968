import { useEffect, useState, useRef } from "react";
import { useLocation, useRoute } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
  } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { formatDate, getStatusColor, formatStatusLabel } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import {
  CalendarDays,
  Wrench,
  Car,
  User,
  ClipboardList,
  ChevronLeft,
  FileText,
  PlusCircle,
  Package,
  Printer,
  Mail,
  MessageCircle,
  FileDown,
  } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
  } from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { apiRequest } from "@/lib/queryClient";
import { fetchAPI } from "@/lib/api";

const JobCardDetails = () => {
  const [, navigate] = useLocation();
  const [, params] = useRoute("/quotation/:id");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const jobCardId = params?.id ? parseInt(params.id) : null;
  const [showAddPartDialog, setShowAddPartDialog] = useState(false);
  const [showAddServiceDialog, setShowAddServiceDialog] = useState(false);
  const [selectedPartId, setSelectedPartId] = useState<string>("");
  const [selectedServiceId, setSelectedServiceId] = useState<string>("");
  const [partPrize, setPartPrize] = useState("0.00");
  const [servicePrize, setServicePrize] = useState("1");
  const [partNotes, setPartNotes] = useState("");
  const [serviceNotes, setServiceNotes] = useState("");
  const [showInvoicePreview, setShowInvoicePreview] = useState(false);
  const [emailContact, setEmailContact] = useState("");
  const [generatedQuotationId, setGeneratedQuotationId] = useState<number | null>(null);
  const [generatedGatePassId, setGeneratedGatePassId] = useState<number | null>(null);
  const [description, setJobDescription] = useState("");
  const [remarks, setRemarks] = useState("");
  const [whatsappContact, setWhatsappContact] = useState("");
  const [invoiceGenerationLoading, setInvoiceGenerationLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);
  const serviceSearchTimeout = useRef<NodeJS.Timeout | null>(null);
  const [partSearch, setPartSearch] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [addedParts, setAddedParts] = useState([]);
  const [submittedParts, setSubmittedParts] = useState([]);
  const [selectedPartObj, setSelectedPartObj] = useState<any>(null);
  const [addedServices, setAddedServices] = useState<any[]>([]);
  const [submittedServices, setSubmittedServices] = useState<any[]>([]);
  const [serviceSearch, setServiceSearch] = useState("");
  const [serviceSearchLoading, setServiceSearchLoading] = useState(false);
  const [filteredServices, setFilteredServices] = useState([]);
  const [selectedServiceObj, setSelectedServiceObj] = useState<any>(null);
  const [customerName, setCustomerName] = useState("");
  const [customerEmail, setCustomerEmail] = useState("");
  const [customerPhone, setCustomerPhone] = useState("");
  const [customerAddress, setCustomerAddress] = useState("");
  const [vehicleMake, setVehicleMake] = useState("");
  const [vehicleModel, setVehicleModel] = useState("");
  const [vehicleReg, setVehicleReg] = useState("");
  const [vehicleColor, setVehicleColor] = useState("");
  const [vehicleEngine, setVehicleEngine] = useState("");
  const [vehicleChassis, setVehicleChassis] = useState("");


  // When jobCard loads, initialize editable fields
  useEffect(() => {
    if (jobCard) {
      setCustomerName(jobCard.customerName || "");
      setCustomerEmail(jobCard.email || "");
      setCustomerPhone(jobCard.phone || "");
      setCustomerAddress(jobCard.address || "");
      setVehicleMake(jobCard.vehicle?.make || "");
      setVehicleModel(jobCard.vehicle?.model_model || "");
      setVehicleReg(jobCard.vehicle?.vehicleRegistrationNumber || "");
      setVehicleColor(jobCard.vehicle?.color || "");
      setVehicleEngine(jobCard.vehicle?.engineNumber || "");
      setVehicleChassis(jobCard.vehicle?.chassisNumber || "");
    }
  }, );

  // Fetch job card data
  const {
    data: jobCard,
    isLoading: jobCardLoading,
    error: jobCardError,
  } = useQuery({
    queryKey: ["/api/gate-pass", jobCardId],
    queryFn: async () => {
      if (!jobCardId) return null;
      const response = await fetchAPI(`/gate-pass/details/${jobCardId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch job card");
      }
      const data = await response.json();
      setEmailContact(data.email);
      setJobDescription(data.jobDescription);
      setRemarks(data.remarks);

      return data;
    },
    enabled: !!jobCardId,
  });

  // Fetch related data - make sure to refetch when job card updates
  // Keep track of the customer name for the job card
  const [customerNameDisplay, setCustomerNameDisplay] = useState<string>("");

  const { data: customer, refetch: refetchCustomer } = useQuery({
    queryKey: ["/api/customers", jobCard?.customerId],
    queryFn: async () => {
      if (!jobCard?.id) return null;
      console.log("Fetching customer with ID:", jobCard?.customerId);
      const response = await fetch(`/api/customers/${jobCard?.customerId}`);
      if (!response.ok) {
        console.error("Error fetching customer data");
        return null;
      }
      const customerData = await response.json();
      console.log("Customer data loaded:", customerData);
      // Update the customer name for display
      if (customerData?.name) {
        setCustomerNameDisplay(customerData.name);
      }
      return customerData;
    },
    enabled: !!jobCard?.customerId,
  });

  // Update contact info when customer data loads
  // useEffect(() => {
  //   if (customer) {
  //     setEmailContact(customer.email || "");
  //     setWhatsappContact(customer.phone || "");
  //   }
  // }, [customer]);

  // const { data: vehicle } = useQuery({
  //   queryKey: ["/api/vehicles", jobCard?.vehicleId],
  //   queryFn: async () => {
  //     if (!jobCard?.vehicleId) return null;
  //     const response = await fetch(`/api/vehicles/${jobCard?.vehicleId}`);
  //     if (!response.ok) {
  //       return null;
  //     }
  //     return response.json();
  //   },
  //   enabled: !!jobCard?.vehicleId
  // });

  // const { data: technician } = useQuery({
  //   queryKey: ["/api/technicians", jobCard?.technicianId],
  //   queryFn: async () => {
  //     if (!jobCard?.technicianId) return null;
  //     const response = await fetch(`/api/technicians/${jobCard?.technicianId}`);
  //     if (!response.ok) {
  //       return null;
  //     }
  //     return response.json();
  //   },
  //   enabled: !!jobCard?.technicianId,
  // });

  // const { data: serviceItems, refetch: refetchServices } = useQuery({
  //   queryKey: ["/api/jobcards", jobCardId, "services"],
  //   queryFn: async () => {
  //     if (!jobCardId) return [];
  //     const response = await fetch(`/api/jobcards/${jobCardId}/services`);
  //     if (!response.ok) {
  //       return [];
  //     }

  //     const services = await response.json();

  //     // Fetch additional service details for each service item to ensure complete data for printing
  //     if (Array.isArray(services) && services.length > 0) {
  //       const updatedServices = await Promise.all(services.map(async (item) => {
  //         if (item.serviceId) {
  //           try {
  //             const serviceDetailsResponse = await fetch(`/api/services/${item.serviceId}`);
  //             if (serviceDetailsResponse.ok) {
  //               const serviceDetails = await serviceDetailsResponse.json();
  //               return {
  //                 ...item,
  //                 name: serviceDetails.name,
  //                 description: serviceDetails.description
  //               };
  //             }
  //           } catch (error) {
  //             console.error(`Error fetching details for service ${item.serviceId}:`, error);
  //           }
  //         }
  //         return item;
  //       }));
  //       return updatedServices;
  //     }

  //     return services;
  //   },
  //   enabled: !!jobCardId
  // });

  // const { data: partItems, refetch: refetchParts } = useQuery({
  //   queryKey: ["/api/jobcards", jobCardId, "parts"],
  //   queryFn: async () => {
  //     if (!jobCardId) return [];
  //     const response = await fetch(`/api/jobcards/${jobCardId}/parts`);
  //     if (!response.ok) {
  //       return [];
  //     }

  //     const parts = await response.json();

  //     // Fetch additional part details for each part item to ensure complete data for printing
  //     if (Array.isArray(parts) && parts.length > 0) {
  //       const updatedParts = await Promise.all(parts.map(async (item) => {
  //         if (item.partId) {
  //           try {
  //             const partDetailsResponse = await fetch(`/api/parts/${item.partId}`);
  //             if (partDetailsResponse.ok) {
  //               const partDetails = await partDetailsResponse.json();
  //               return {
  //                 ...item,
  //                 name: partDetails.name,
  //                 partNumber: partDetails.partNumber,
  //                 description: partDetails.description
  //               };
  //             }
  //           } catch (error) {
  //             console.error(`Error fetching details for part ${item.partId}:`, error);
  //           }
  //         }
  //         return item;
  //       }));
  //       return updatedParts;
  //     }

  //     return parts;
  //   },
  //   enabled: !!jobCardId
  // });

  // // Fetch all available parts
  // const { data: availableParts } = useQuery({
  //   queryKey: ["/api/parts"],
  //   queryFn: async () => {
  //     const response = await fetch("/api/parts");
  //     if (!response.ok) {
  //       return [];
  //     }
  //     return response.json();
  //   }
  // });

  // // Fetch all available services
  // const { data: availableServices } = useQuery({
  //   queryKey: ["/api/services"],
  //   queryFn: async () => {
  //     const response = await fetch("/api/services");
  //     if (!response.ok) {
  //       return [];
  //     }
  //     return response.json();
  //   }
  // });

  const handlePartSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPartSearch(value);
    setSearchLoading(true);
    if (searchTimeout.current) clearTimeout(searchTimeout.current);
    searchTimeout.current = setTimeout(async () => {
      if (value.trim().length === 0) {
        setSearchResults([]);
        setSearchLoading(false);
        return;
      }
      try {
        const response = await fetchAPI(
          `/gate-pass/vehicle-parts/search?keyword=${encodeURIComponent(
            value.trim()
          )}`
        );
        if (!response.ok) {
          setSearchResults([]);
        } else {
          const results = await response.json();
          setSearchResults(results);
        }
      } catch {
        setSearchResults([]);
      }
      setSearchLoading(false);
    }, 400);
  };

  const handleServiceSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setServiceSearch(value);
    setServiceSearchLoading(true);
    if (serviceSearchTimeout.current)
      clearTimeout(serviceSearchTimeout.current);
    serviceSearchTimeout.current = setTimeout(async () => {
      if (value.trim().length === 0) {
        setFilteredServices([]);
        setServiceSearchLoading(false);
        return;
      }
      try {
        const response = await fetchAPI(
          `/gate-pass/services/search?keyword=${encodeURIComponent(
            value.trim()
          )}`
        );
        if (!response.ok) {
          setFilteredServices([]);
        } else {
          const results = await response.json();
          setFilteredServices(results);
        }
      } catch {
        setFilteredServices([]);
      }
      setServiceSearchLoading(false);
    }, 400);
  };

  const saveQuotation = async () => {
    try {
      // Show loading state
      setInvoiceGenerationLoading(true);
      
      // Prepare data for API request - only send the necessary data
      const quotationData = {
        gatePassId: jobCard?.id,
        partsUsed: submittedParts,
        servicesUsed: submittedServices,
        jobDescription: description || '',
        remarks: remarks || '',
        customerName: customerName,
        customerEmail: customerEmail,
        customerPhone: customerPhone,
        customerAddress: customerAddress,
        vehicleMake: vehicleMake,
        vehicleModel: vehicleModel,
        vehicleRegistrationNumber: vehicleReg,
        vehicleColor: vehicleColor,
        vehicleEngineNumber: vehicleEngine,
        vehicleChassisNumber: vehicleChassis,
        totalAmount: calculateTotalCost().toFixed(2),
        tax: '5'
      };

      // Send data to backend
      const response = await fetchAPI('/gate-pass/quotation/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quotationData),
      });

      if (!response.ok) {
        throw new Error('Failed to save quotation');
      }

      const result = await response.json();

      setGeneratedQuotationId(result.id);
      setGeneratedGatePassId(result.gatePassId);

      toast({
        title: "Quotation Saved",
        description: "Quotation has been saved successfully",
      });

      // open share modal
      setShowInvoicePreview(true);


      // Open the generated quotation in a new window
      // if (result.fileUrl) {
      //   window.open(`${result.fileUrl}`, "_blank");
      // }

      return result;
    } catch (error) {
      console.error("Error saving quotation:", error);
      toast({
        title: "Error",
        description: "Failed to save quotation. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setInvoiceGenerationLoading(false);
    }
  };



  // Simple function to add a part to the job card
  // const addPart = async (data: any) => {
  //   try {
  //     const response = await fetch(`/api/jobcards/${jobCardId}/parts`, {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify(data),
  //     });

  //     if (!response.ok) {
  //       const errorData = await response.json();
  //       throw new Error(errorData.message || "Failed to add part to job card");
  //     }

  //     toast({
  //       title: "Part Added",
  //       description: "The part has been added to the job card successfully",
  //     });

  //     setShowAddPartDialog(false);
  //     setSelectedPartId("");
  //     setPartPrize("0.00");
  //     setPartNotes("");

  //     // Refetch the part items
  //     refetchParts();
  //     return true;
  //   } catch (error) {
  //     console.error("Error adding part:", error);

  //     toast({
  //       title: "Error",
  //       description:
  //         error instanceof Error
  //           ? error.message
  //           : "Failed to add part to job card. Please try again.",
  //       variant: "destructive",
  //     });
  //     return false;
  //   }
  // };

  // Simple function to add a service to the job card - using the same pattern as addPart
  // const addService = async (data: any) => {
  //   try {
  //     const response = await fetch(`/api/jobcards/${jobCardId}/services`, {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify(data),
  //     });

  //     if (!response.ok) {
  //       const errorData = await response.json();
  //       throw new Error(
  //         errorData.message || "Failed to add service to job card"
  //       );
  //     }

  //     toast({
  //       title: "Service Added",
  //       description: "The service has been added to the job card successfully",
  //     });

  //     setShowAddServiceDialog(false);
  //     setSelectedServiceId("");
  //     setServicePrize("1");
  //     setServiceNotes("");

  //     // Refetch service items
  //     queryClient.invalidateQueries({
  //       queryKey: ["/api/jobcards", jobCardId, "services"],
  //     });
  //     return true;
  //   } catch (error) {
  //     console.error("Error adding service:", error);

  //     toast({
  //       title: "Error",
  //       description:
  //         error instanceof Error
  //           ? error.message
  //           : "Failed to add service to job card. Please try again.",
  //       variant: "destructive",
  //     });
  //     return false;
  //   }
  // };

  const handleAddSinglePart = () => {
    if (!selectedPartObj) return;

    setAddedParts((prev) => [
      ...prev,
      {
        id: selectedPartObj.id,
        itemName: selectedPartObj.itemName,
        price: partPrize,
      },
    ]);

    // Reset form
    setSelectedPartId("");
    setPartSearch("");
    setPartPrize("");
    setPartNotes("");
    setSelectedPartObj(null); // reset selected part object
    setSearchResults([]);
  };

  const handleRemovePart = (index) => {
    setAddedParts((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmitAllParts = () => {
    setSubmittedParts((prev) => [...prev, ...addedParts]);
    setAddedParts([]);
    setShowAddPartDialog(false);
  };

  const handleAddSingleService = () => {
    if (!selectedServiceObj) return;

    setAddedServices((prev) => [
      ...prev,
      {
        id: selectedServiceObj.id,
        itemName: selectedServiceObj.itemName,
        price: servicePrize,
      },
    ]);

    // Reset form
    setSelectedServiceId("");
    setServiceSearch("");
    setServicePrize("");
    setServiceNotes("");
    setSelectedServiceObj(null);
    setFilteredServices([]);
  };

  const handleRemoveService = (index: number) => {
    setAddedServices((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmitAllServices = () => {
    setSubmittedServices((prev) => [...prev, ...addedServices]);
    setAddedServices([]);
    setShowAddServiceDialog(false);
  };

  // const handleAddService = async () => {
  //   if (!selectedServiceId) {
  //     toast({
  //       title: "Select a Service",
  //       description: "Please select a service to add to the job card",
  //       variant: "destructive",
  //     });
  //     return;
  //   }

  //   if (!availableServices || !Array.isArray(availableServices)) {
  //     toast({
  //       title: "Error",
  //       description: "Services list is not available",
  //       variant: "destructive",
  //     });
  //     return;
  //   }

  //   const selectedService = availableServices.find(
  //     (service: any) => service.id.toString() === selectedServiceId
  //   );
  //   if (!selectedService) {
  //     toast({
  //       title: "Error",
  //       description: "Selected service not found",
  //       variant: "destructive",
  //     });
  //     return;
  //   }

  //   const serviceData = {
  //     serviceId: parseInt(selectedServiceId),
  //     price: parseInt(servicePrize),
  //     // price: selectedService.price,
  //     status: "PENDING",
  //     notes: serviceNotes || undefined,
  //   };

  //   console.log("Adding service:", serviceData);
  //   await addService(serviceData);
  // };

  // const generateJobCard = async () => {
  //   try {
  //     setInvoiceGenerationLoading(true);
  //     // Ensure we have the job card ID
  //     if (!jobCard || !jobCard?.id) {
  //       throw new Error("Job card information is missing");
  //     }

  //     // Call the API to generate an invoice with the job card ID
  //     const response = await fetch("/api/invoices", {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify({
  //         jobCardId: jobCard?.id,
  //       }),
  //     });

  //     if (!response.ok) {
  //       const errorData = await response.json();
  //       throw new Error(errorData.message || "Failed to generate invoice");
  //     }

  //     const data = await response.json();

  //     // Success message
  //     toast({
  //       title: "Invoice Generated",
  //       description: `Invoice has been created successfully`,
  //     });

  //     // Log the response for debugging
  //     console.log("Invoice data:", data);

  //     setShowInvoicePreview(false);

  //     // Navigate directly to the view-only invoice page
  //     if (data && data.invoice && data.invoice.id) {
  //       // Navigate to the invoice detail page
  //       navigate(`/invoices/${data.invoice.id}`);
  //     } else if (data && data.id) {
  //       // Older API response format
  //       navigate(`/invoices/${data.id}`);
  //     } else {
  //       // Show an error if we couldn't determine the invoice ID
  //       toast({
  //         title: "Error",
  //         description:
  //           "Could not retrieve invoice information, please try again",
  //         variant: "destructive",
  //       });
  //     }
  //   } catch (error: any) {
  //     toast({
  //       title: "Error",
  //       description: error.message || "Failed to generate invoice",
  //       variant: "destructive",
  //     });
  //     console.error("Invoice generation error:", error);
  //   } finally {
  //     setInvoiceGenerationLoading(false);
  //   }
  // };
  const buildInvoiceHtml = () => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Job Card Invoice</title>
  <style>
    * { box-sizing: border-box; }
    html, body { height: 100%; margin: 0; font-family: Arial, sans-serif; font-size: 13px; }
    body { display: flex; flex-direction: column; padding: 30px; }
    .content { flex: 1; }
    .header { text-align: center; font-weight: bold; font-size: 16px; }
    .sub-header { text-align: center; font-size: 12px; margin-bottom: 20px; }
    table { width: 100%; border-collapse: collapse; border: 1px solid black }
    .details-table td { border: 1px solid #000; padding: 6px; vertical-align: top; }
    .billing-table th { border: 1px solid #000; padding: 10px; vertical-align: top; height: 40px; background-color: #f2f2f2; text-align: left; }
    .billing-table td { border-bottom: None; border-right: 1px solid black; padding: 10px; vertical-align: top; height: 40px; }
    .summary-table td { border-bottom: None; padding: 10px; height: 30px; }
    .summary-table .label { text-align: right; width: 85%; }
    .summary-table .value { text-align: right; width: 15%; border: 1px solid #000; }
    .grand-total-row { font-weight: bold; }
    .declaration { border-top: 1px solid #000; margin-top: 30px; padding-top: 10px; }
    .signature { margin-top: 60px; text-align: right; }
  </style>
</head>
<body>
  <div class="content">
    <div class="header">
      M R S AUTO MAINTENANCE L.L.C
    </div>
    <div class="sub-header">
      Tel: +971 55 994 1284, +971 55 994 1285 | Email: <EMAIL>
    </div>
    <table class="details-table">
      <tr>
        <td><strong>Job Card No:</strong> ${jobCard?.id || ""}</td>
        <td><strong>Date:</strong> ${
          jobCard?.createdAt
            ? new Date(jobCard.createdAt).toLocaleDateString()
            : ""
        }</td>
        <td><strong>Time:</strong> ${
          jobCard?.createdAt
            ? new Date(jobCard.createdAt).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })
            : ""
        }</td>
      </tr>
      <tr>
        <td><strong>Driver Name:</strong> ${jobCard?.customerName || ""}</td>
        <td><strong>Make & Model:</strong> ${jobCard?.vehicle?.make || ""} ${
      jobCard?.vehicle?.model_model || ""
    }</td>
        <td><strong>Assigned to:</strong> ${"Unassigned"}</td>
      </tr>
      <tr>
        <td><strong>Chassis No:</strong> ${
          jobCard?.vehicle?.chassisNumber || ""
        }</td>
        <td><strong>Engine No:</strong> ${
          jobCard?.vehicle?.engineNumber || ""
        }</td>
        <td><strong>Reg No:</strong> ${
          jobCard?.vehicle?.vehicleRegistrationNumber || ""
        }</td>
      </tr>
      <tr>
        <td><strong>Delivery Date:</strong> ${
          jobCard?.estimatedDeliveryDate
            ? new Date(jobCard.estimatedDeliveryDate).toLocaleDateString()
            : ""
        }</td>
        <td><strong>Color:</strong> ${jobCard?.vehicle?.color || ""}</td>
        <td><strong>ODO Meter:</strong> ${jobCard?.vehicle?.odometer || ""}</td>
      </tr>
      <tr>
        <td><strong>Mobile No:</strong> ${jobCard?.phone || ""}</td>
        <td colspan="2"><strong>Veh. Reg. Card:</strong> ${
          jobCard?.vehicle?.registrationCard || "N"
        }</td>
      </tr>
    </table>
    <br>
    <table class="billing-table">
      <thead>
        <tr>
          <th style="width: 5%;">S.N</th>
          <th style="width: 55%;">Description</th>
          <th style="width: 10%;">Qty</th>
          <th style="width: 10%;">Unit</th>
          <th style="width: 10%;">Price</th>
          <th style="width: 10%;">Amount (AED)</th>
        </tr>
      </thead>
      <tbody>
        ${[...(submittedParts || []), ...(submittedServices || [])]
          .map(
            (item: any, idx: number) => `
            <tr>
              <td>${idx + 1}</td>
              <td>${item.itemName || ""}</td>
              <td>1.00</td>
              <td>Pcs</td>
              <td>${parseFloat(item.price).toFixed(2)}</td>
              <td>${parseFloat(item.price).toFixed(2)}</td>
            </tr>
          `
          )
          .join("")}
        ${(() => {
          const totalRows =
            (submittedParts?.length || 0) + (submittedServices?.length || 0);
          let emptyRows = "";
          for (let i = totalRows; i < 4; i++) {
            emptyRows += `<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>`;
          }
          return emptyRows;
        })()}
      </tbody>
    </table>
    <br>
    <table class="summary-table">
      <tr>
        <td class="label">Total</td>
        <td class="value">${calculateTotalCost().toFixed(2)}</td>
      </tr>
      <tr>
        <td class="label">Less : Excess</td>
        <td class="value">0.00</td>
      </tr>
      <tr>
        <td class="label">Less : Discount</td>
        <td class="value">0.00</td>
      </tr>
      <tr>
        <td class="label">Add : VAT @ 5.00%</td>
        <td class="value">${(calculateTotalCost() * 0.05).toFixed(2)}</td>
      </tr>
      <tr class="grand-total-row">
        <td class="label">Grand Total</td>
        <td class="value">${(calculateTotalCost() * 1.05).toFixed(2)}</td>
      </tr>
    </table>
    <br><strong>Dirhams ${
      Math.round(calculateTotalCost() * 1.05) || "Zero"
    } Only</strong>
  </div>
  <div class="declaration">
    <strong>DECLARATION</strong><br>
    I hereby authorize your garage to repair my vehicle.<br><br>
    Name: ______________________ <br>
    Signature: __________________
  </div>
  <div class="signature">
    for <strong>M R S Auto Maintenance LLC</strong><br>
    Authorised Signatory
  </div>
</body>
</html>
  `;
  // Function to print job card
  const printJobCard = () => {
    // refetchParts();
    // refetchServices();
    // refetchCustomer();
    const html = buildInvoiceHtml(); 
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      toast({
        title: "Print Error",
        description:
          "Unable to open print window. Please check your popup blocker settings.",
        variant: "destructive",
      });
      return;
    }

    // Use submittedParts and submittedServices for printing
    printWindow.document.write(html);

    setTimeout(() => {
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.addEventListener("afterprint", () => {
        printWindow.close();
      });
    }, 1000);
  };

const sendInvoiceEmail = async (receiverEmail: string) => {
  await fetchAPI('/gate-pass/send-invoice-email', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ receiverEmail, generatedQuotationId, generatedGatePassId }),
  }); 
};





  
  if (jobCardLoading) {
    return (
      <div className="p-8 flex justify-center">
        <div className="animate-pulse flex flex-col space-y-4 w-full max-w-4xl">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (jobCardError || !jobCard) {
    return (
      <div className="p-8 flex flex-col items-center justify-center space-y-4">
        <h2 className="text-2xl font-bold text-red-600">
          Error Loading Job Card
        </h2>
        <p className="text-gray-600">
          Unable to load the job card details. It may have been deleted or does
          not exist.
        </p>
        <Button
          variant="outline"
          onClick={() => navigate("/quotation")}
          className="mt-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" /> Return to Job Cards
        </Button>
      </div>
    );
  }


  const calculateTotalCost = () => {
    const serviceCost: number = submittedServices
      ? submittedServices.reduce(
          (total: number, service: any) => total + parseInt(service.price),
          0
        )
      : 0;
    const partsCost: number = submittedParts
      ? submittedParts.reduce(
          (total: number, part: any) => total + parseInt(part.price),
          0
        )
      : 0;
    return serviceCost + partsCost;
  };

  return (
    <div className="py-6 print-container">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Button
              variant="outline"
              onClick={() => navigate("/quotation")}
              className="mr-4 print-hidden"
            >
              <ChevronLeft className="mr-2 h-4 w-4" /> Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Quotation No. {jobCard?.id}
              </h1>
              <p className="text-gray-600">
                Created on {formatDate(jobCard?.createdAt)}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-4 print-hidden">
            <Badge className={getStatusColor(jobCard?.status)}>
              {formatStatusLabel(jobCard?.status)}
            </Badge>
            {/* <Button
              onClick={printJobCard}
              variant="outline"
              className="flex items-center gap-1"
            >
              <Printer className="h-4 w-4" /> Print
            </Button> */}
            
              {/* onClick={() => setShowInvoicePreview(true)} */}
            <Button
              onClick={() => saveQuotation()}
              className="flex items-center gap-1"
              disabled={jobCard?.status === "INVOICED"}
            >
              <FileText className="h-4 w-4" />
              Generate Quotation
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <User className="mr-2 h-5 w-5 text-primary" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <Label htmlFor="customer-name">Customer Name</Label>
                  <Input
                    id="customer-name"
                    className="font-medium"
                    value={customerName}
                    onChange={e => setCustomerName(e.target.value)}
                    placeholder="Customer Name"
                  />
                </div>
                <div>
                  <Label htmlFor="customer-email">Email</Label>
                  <Input
                    id="customer-email"
                    className="text-sm"
                    value={customerEmail}
                    onChange={e => setCustomerEmail(e.target.value)}
                    placeholder="Email"
                  />
                </div>
                <div>
                  <Label htmlFor="customer-phone">Phone</Label>
                  <Input
                    id="customer-phone"
                    className="text-sm"
                    value={customerPhone}
                    onChange={e => setCustomerPhone(e.target.value)}
                    placeholder="Phone"
                  />
                </div>
                <div>
                  <Label htmlFor="customer-address">Address</Label>
                  <Input
                    id="customer-address"
                    className="text-sm"
                    value={customerAddress}
                    onChange={e => setCustomerAddress(e.target.value)}
                    placeholder="Address"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <Car className="mr-2 h-5 w-5 text-primary" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <div className="flex-1">
                    <Label htmlFor="vehicle-make">Make</Label>
                    <Input
                      id="vehicle-make"
                      className="font-medium"
                      value={vehicleMake}
                      onChange={e => setVehicleMake(e.target.value)}
                      placeholder="Make"
                    />
                  </div>
                  <div className="flex-1">
                    <Label htmlFor="vehicle-model">Model</Label>
                    <Input
                      id="vehicle-model"
                      className="font-medium"
                      value={vehicleModel}
                      onChange={e => setVehicleModel(e.target.value)}
                      placeholder="Model"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-x-4 text-sm">
                  <div>
                    <Label htmlFor="vehicle-reg">Registration Number</Label>
                    <Input
                      id="vehicle-reg"
                      value={vehicleReg}
                      onChange={e => setVehicleReg(e.target.value)}
                      placeholder="Registration Number"
                    />
                  </div>
                  <div>
                    <Label htmlFor="vehicle-color">Color</Label>
                    <Input
                      id="vehicle-color"
                      value={vehicleColor}
                      onChange={e => setVehicleColor(e.target.value)}
                      placeholder="Color"
                    />
                  </div>
                  <div>
                    <Label htmlFor="vehicle-engine">Engine Number</Label>
                    <Input
                      id="vehicle-engine"
                      value={vehicleEngine}
                      onChange={e => setVehicleEngine(e.target.value)}
                      placeholder="Engine Number"
                    />
                  </div>
                  <div>
                    <Label htmlFor="vehicle-chassis">Chassis Number</Label>
                    <Input
                      id="vehicle-chassis"
                      value={vehicleChassis}
                      onChange={e => setVehicleChassis(e.target.value)}
                      placeholder="Chassis Number"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <Wrench className="mr-2 h-5 w-5 text-primary" />
                Service Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <p className="text-gray-500">Assigned to:</p>
                  <p className="font-medium">
                    {technician?.name || "Unassigned"}
                  </p>
                </div>
                <div className="flex justify-between">
                  <p className="text-gray-500">Estimated completion:</p>
                  <p>
                    {jobCard?.estimatedDeliveryDate
                      ? formatDate(jobCard?.estimatedDeliveryDate)
                      : "Not set"}
                  </p>
                </div>
                <div className="flex justify-between">
                  <p className="text-gray-500">Cost estimate:</p>
                  <p className="font-medium">
                    {`AED ${calculateTotalCost().toFixed(2)}`}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card> */}
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <ClipboardList className="mr-2 h-5 w-5 text-primary" />
              Job Description
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-base mb-1">Description</h3>
                {/* <p className="text-gray-700"> */}
                {/* {jobCard?.jobDescription || "No description provided"} */}
                {/* </p> */}

                {/* input field */}
                <textarea
                  className="w-full border border-gray-300 rounded-md p-2"
                  value={description}
                  onChange={(e) => setJobDescription(e.target.value)}
                />
              </div>
              <div>
                <h3 className="font-medium text-base mb-1">Notes</h3>
                {/* <p className="text-gray-700">
                  {jobCard?.remarks || "No notes provided"}
                </p> */}
                <textarea
                  className="w-full border border-gray-300 rounded-md p-2"
                  value={remarks}
                  onChange={(e) => setRemarks(e.target.value)}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-base mb-1">
                    Insurance Claim
                  </h3>
                  <p className="text-gray-700">
                    {jobCard?.isInsuranceClaim ? "Yes" : "No"}
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-base mb-1">Warranty Claim</h3>
                  <p className="text-gray-700">
                    {jobCard?.isWarrantyClaim ? "Yes" : "No"}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="print-break-before"></div>

        <Tabs defaultValue="parts" className="w-full print-hidden">
          <TabsList className="mb-4">
            <TabsTrigger value="parts">Parts</TabsTrigger>
            <TabsTrigger value="services">Services</TabsTrigger>
          </TabsList>
          <TabsContent value="parts" data-parts-content>
            <Card className="print-section">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center text-lg">
                  <Package className="mr-2 h-5 w-5 text-primary" />
                  Parts
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAddPartDialog(true)}
                  className="print-hidden"
                >
                  <PlusCircle className="h-4 w-4 mr-1" /> Add Part
                </Button>
              </CardHeader>
              <CardContent>
                {submittedParts && submittedParts.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-gray-50 border-b">
                          <th className="text-left p-2">Part</th>
                          <th className="text-center p-2">Price</th>
                        </tr>
                      </thead>
                      <tbody>
                        {submittedParts.map((part: any, idx: number) => (
                          <tr key={idx} className="border-b">
                            <td className="p-2">
                              <div>
                                <p className="font-medium">
                                  {part.itemName || `Part #${part.id}`}
                                </p>
                                {part.notes && (
                                  <p className="text-sm text-gray-500">
                                    {part.notes}
                                  </p>
                                )}
                              </div>
                            </td>
                            <td className="text-center p-2">{part.price}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-gray-500 py-4 text-center">
                    No parts added to this job card yet.
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="services" data-services-content>
            <Card className="print-section">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center text-lg">
                  <Wrench className="mr-2 h-5 w-5 text-primary" />
                  Services
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAddServiceDialog(true)}
                  className="print-hidden"
                >
                  <PlusCircle className="h-4 w-4 mr-1" /> Add Service
                </Button>
              </CardHeader>
              <CardContent>
                {submittedServices && submittedServices.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-gray-50 border-b">
                          <th className="text-left p-2">Service</th>
                          <th className="text-center p-2">Prize</th>
                        </tr>
                      </thead>
                      <tbody>
                        {submittedServices.map((service: any, idx: number) => (
                          <tr key={idx} className="border-b">
                            <td className="p-2">
                              <div>
                                <p className="font-medium">
                                  {service.itemName || `Service #${service.id}`}
                                </p>
                                {service.notes && (
                                  <p className="text-sm text-gray-500">
                                    {service.notes}
                                  </p>
                                )}
                              </div>
                            </td>
                            <td className="text-center p-2">{service.price}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-gray-500 py-4 text-center">
                    No services added to this job card yet.
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Card className="mt-6 print-show">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <CalendarDays className="mr-2 h-5 w-5 text-primary" />
              Total Cost Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-2 max-w-xs ml-auto">
              <div className="flex justify-between">
                <span>Parts Subtotal:</span>
                <span>
                  AED{" "}
                  {submittedParts
                    ? submittedParts
                        .reduce(
                          (total: number, part: any) =>
                            total + parseInt(part.price),
                          0
                        )
                        .toFixed(2)
                    : "0.00"}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Services Subtotal:</span>
                <span>
                  AED{" "}
                  {submittedServices
                    ? submittedServices
                        .reduce(
                          (total: number, service: any) =>
                            total + parseInt(service.price),
                          0
                        )
                        .toFixed(2)
                    : "0.00"}
                </span>
              </div>
              <div className="flex justify-between font-semibold pt-2 border-t">
                <span>Total:</span>
                <span>AED {calculateTotalCost().toFixed(2)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer Signature Section - Always visible but styled differently for print */}
        <div className="mt-8 print-section signature-section">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Customer Approval</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <p className="text-sm">
                  I,{" "}
                  <span className="font-semibold">
                    {customerNameDisplay ||
                      jobCard?.customerName ||
                      "____________________"}
                  </span>
                  , confirm that the above services and parts have been
                  explained to me, and I authorize MRS Automaintenance LLP to
                  proceed with the work as described.
                </p>

                <div className="grid grid-cols-2 gap-8 mt-6">
                  <div className="space-y-2">
                    <div className="border-b border-dashed border-gray-400 h-10 flex items-end signature-line">
                      <p className="text-xs text-gray-500">
                        Customer Signature
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="border-b border-dashed border-gray-400 h-10 flex items-end signature-line">
                      <p className="text-xs text-gray-500">Date</p>
                    </div>
                  </div>
                </div>

                <div className="pt-6 border-t border-gray-200 mt-6">
                  <p className="text-xs text-gray-600">
                    MRS Automaintenance LLP | Dubai, UAE | Tel: +971 4 123 4567
                    | Email: <EMAIL>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Add Part Dialog */}
      <Dialog open={showAddPartDialog} onOpenChange={setShowAddPartDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Parts</DialogTitle>
            <DialogDescription>
              Add one or more parts to this job card with respective price and
              notes.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Form to add a single part */}
            <div className="space-y-2 relative">
              <Label htmlFor="part-search">Search Part</Label>
              <Input
                id="part-search"
                placeholder="Type part name or number"
                value={partSearch}
                onChange={handlePartSearch}
                autoComplete="off"
              />
              {searchLoading && (
                <div className="text-xs text-gray-400">Searching...</div>
              )}
              {partSearch && searchResults.length > 0 && (
                <div className="absolute left-0 right-0 top-full mt-1 z-50 max-h-48 overflow-y-auto border rounded bg-white shadow">
                  {searchResults.map((part) => (
                    <div
                      key={part.id}
                      className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                        selectedPartId === part.id.toString()
                          ? "bg-blue-100"
                          : ""
                      }`}
                      onClick={() => {
                        setSelectedPartId(part.id.toString());
                        setPartSearch(part.itemName);
                        setSelectedPartObj(part); // <-- store the selected part object
                        setSearchResults([]);
                      }}
                    >
                      <div className="font-medium">{part.itemName}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="price">Price</Label>
              <Input
                id="price"
                type="number"
                value={partPrize}
                onChange={(e) => setPartPrize(e.target.value)}
                min={1}
              />
            </div>

            {/* <div className="space-y-2">
        <Label htmlFor="part-notes">Notes (Optional)</Label>
        <Input
          id="part-notes"
          value={partNotes}
          onChange={(e) => setPartNotes(e.target.value)}
          placeholder="Any additional notes about this part"
        />
      </div> */}

            <Button
              variant="secondary"
              onClick={handleAddSinglePart}
              disabled={!selectedPartId || !partPrize}
            >
              + Add to List
            </Button>

            {/* List of added parts */}
            {addedParts.length > 0 && (
              <div className="mt-4 space-y-3 border-t pt-4">
                <div className="font-medium">Parts to be added:</div>
                <div className="max-h-48 overflow-y-auto space-y-3 pr-1">
                  {addedParts.map((p, idx) => (
                    <div
                      key={idx}
                      className="flex justify-between items-start border p-2 rounded"
                    >
                      <div>
                        <div className="font-semibold">{p.itemName}</div>
                        <div className="text-sm text-muted-foreground">
                          Price: AED {p.price}
                        </div>
                      </div>
                      <button
                        onClick={() => handleRemovePart(idx)}
                        className="text-red-500 text-sm hover:underline"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAddPartDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitAllParts}
              disabled={addedParts.length === 0}
            >
              Submit All
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Service Dialog */}
      <Dialog
        open={showAddServiceDialog}
        onOpenChange={setShowAddServiceDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Services</DialogTitle>
            <DialogDescription>
              Add one or more services to this job card with respective price
              and notes.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Form to add a single service */}
            <div className="space-y-2 relative">
              <Label htmlFor="service-search">Search Service</Label>
              <Input
                id="service-search"
                placeholder="Type service name"
                value={serviceSearch}
                onChange={handleServiceSearch}
                autoComplete="off"
              />
              {serviceSearchLoading && (
                <div className="text-xs text-gray-400">Searching...</div>
              )}
              {serviceSearch && filteredServices.length > 0 && (
                <div className="absolute left-0 right-0 top-full mt-1 z-50 max-h-48 overflow-y-auto border rounded bg-white shadow">
                  {filteredServices.map((service) => (
                    <div
                      key={service.id}
                      className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                        selectedServiceId === service.id.toString()
                          ? "bg-blue-100"
                          : ""
                      }`}
                      onClick={() => {
                        setSelectedServiceId(service.id.toString());
                        setServiceSearch(service.itemName);
                        setSelectedServiceObj(service); // <-- store the selected service object
                        setFilteredServices([]);
                      }}
                    >
                      <div className="font-medium">{service.itemName}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="service-price">Price</Label>
              <Input
                id="service-price"
                type="number"
                value={servicePrize}
                onChange={(e) => setServicePrize(e.target.value)}
                min={1}
              />
            </div>

            {/* <div className="space-y-2">
              <Label htmlFor="service-notes">Notes (Optional)</Label>
              <Input
                id="service-notes"
                value={serviceNotes}
                onChange={(e) => setServiceNotes(e.target.value)}
                placeholder="Any additional notes about this service"
              />
            </div> */}

            <Button
              variant="secondary"
              onClick={handleAddSingleService}
              disabled={!selectedServiceId || !servicePrize}
            >
              + Add to List
            </Button>

            {/* List of added services */}
            {addedServices.length > 0 && (
              <div className="mt-4 space-y-3 border-t pt-4">
                <div className="font-medium">Services to be added:</div>
                <div className="max-h-48 overflow-y-auto space-y-3 pr-1">
                  {addedServices.map((s, idx) => (
                    <div
                      key={idx}
                      className="flex justify-between items-start border p-2 rounded"
                    >
                      <div>
                        <div className="font-semibold">{s.itemName}</div>
                        <div className="text-sm text-muted-foreground">
                          Price: AED {s.price}
                        </div>
                      </div>
                      <button
                        onClick={() => handleRemoveService(idx)}
                        className="text-red-500 text-sm hover:underline"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAddServiceDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitAllServices}
              disabled={addedServices.length === 0}
            >
              Submit All
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Invoice Preview Dialog */}
      <Dialog open={showInvoicePreview} onOpenChange={setShowInvoicePreview}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle> Quotation generated successfully </DialogTitle>
            <DialogDescription>
              Please preview and send quotation to customer via email or print.
            </DialogDescription>
          </DialogHeader>

          {/* <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 py-2">
            <div>
              <h3 className="font-semibold mb-2">Customer Information</h3>
              {customer ? (
                <div className="space-y-1 text-sm">
                  <p className="font-medium">{customer.name}</p>
                  <p>{customer.email}</p>
                  <p>{customer.phone}</p>
                </div>
              ) : (
                <p className="text-gray-500">No customer information available</p>
              )}
            </div>
            <div>
              <h3 className="font-semibold mb-2">Vehicle Information</h3>
              {vehicle ? (
                <div className="space-y-1 text-sm">
                  <p className="font-medium">{vehicle.make} {vehicle.model}</p>
                  <p>Registration: {vehicle.registrationNumber}</p>
                  <p>VIN: {vehicle.vin}</p>
                </div>
              ) : (
                <p className="text-gray-500">No vehicle information available</p>
              )}
            </div>
          </div> */}

          {/* <div className="mt-4">
            <h3 className="font-semibold mb-2">Services and Parts</h3>
            <div className="text-sm border rounded-md p-3 bg-gray-50 max-h-40 overflow-y-auto">
              {partItems && partItems.length > 0 ? (
                <>
                  <p className="font-medium text-xs uppercase text-gray-500 mb-1">Parts</p>
                  <ul className="mb-3 ml-4 list-disc">
                    {partItems.map((part: any) => (
                      <li key={`part-${part.id}`} className="mb-1">
                        {part.name || `Part #${part.partId}`}
 - {part.price}x - AED {part.price.toFixed(2)}
                      </li>
                    ))}
                  </ul>
                </>
              ) : null}
              
              {serviceItems && serviceItems.length > 0 ? (
                <>
                  <p className="font-medium text-xs uppercase text-gray-500 mb-1">Services</p>
                  <ul className="ml-4 list-disc">
                    {serviceItems.map((service: any) => (
                      <li key={`service-${service.id}`} className="mb-1">
                        {service.name || `Service #${service.serviceId}`} - {service.price}x - AED {service.price.toFixed(2)}
                      </li>
                    ))}
                  </ul>
                </>
              ) : null}
              
              {(!partItems || partItems.length === 0) && (!serviceItems || serviceItems.length === 0) && (
                <p className="text-gray-500 italic">No services or parts added to this job card yet.</p>
              )}
            </div>
          </div> */}

          <div className="border rounded-md p-4 bg-gray-50 mt-4">
            {/* <h3 className="font-semibold mb-2">Invoice Information</h3> */}
            {/* <div className="space-y-3 mb-4">
              <div className="space-y-1">
                <Label htmlFor="invoice-description">Description</Label>
                <Textarea 
                  id="invoice-description" 
                  placeholder="Vehicle service and maintenance"
                  className="h-20"
                  defaultValue={jobCard?.description || `Vehicle service for ${vehicle?.make} ${vehicle?.model} (${vehicle?.registrationNumber})`}
                />
                <p className="text-xs text-gray-500">Enter a brief description of the service provided</p>
              </div>
              
              <div className="space-y-1">
                <Label htmlFor="invoice-remarks">Remarks</Label>
                <Textarea 
                  id="invoice-remarks" 
                  placeholder="Thank you for your business!"
                  className="h-20"
                  defaultValue={jobCard?.notes || `Thank you for choosing MRS Automaintenance LLP for your vehicle service needs.`}
                />
                <p className="text-xs text-gray-500">Add any notes or comments to appear on the invoice</p>
              </div>
            </div> */}

            <h3 className="font-semibold mb-2">Contact Information</h3>
            <p className="text-sm text-gray-600 mb-2">
              Add contact details for sending the invoice:
            </p>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-blue-500" />
                <Label htmlFor="email-contact">Email</Label>
                <Input
                  id="email-contact"
                  value={emailContact}
                  onChange={(e) => setEmailContact(e.target.value)}
                  placeholder="<EMAIL>"
                  className="flex-1 ml-2"
                />
              </div>

              {/* <div className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4 text-green-500" />
                <Label htmlFor="whatsapp-contact">WhatsApp</Label>
                <Input 
                  id="whatsapp-contact" 
                  value={whatsappContact}
                  onChange={(e) => setWhatsappContact(e.target.value)}
                  placeholder="+971 50 123 4567" 
                  className="flex-1 ml-2"
                />
              </div> */}
            </div>
          </div>

          <div className="flex items-center justify-between mt-6 w-full">
            <div className="flex space-x-2 w-full justify-end">
              <Button
                type="button"
                variant="outline"
                className="flex items-center gap-1 bg-blue-50 text-blue-700 hover:bg-blue-100"
                onClick={() => sendInvoiceEmail(emailContact)}
              >
                <Mail className="h-4 w-4" /> Send via Email
              </Button>

              {/* <Button
                type="button"
                variant="outline"
                className="flex items-center gap-1 bg-green-50 text-green-700 hover:bg-green-100"
                onClick={() => {
                  if (!whatsappContact) {
                    toast({
                      title: "WhatsApp Number Required",
                      description:
                        "Please enter a WhatsApp number to send the invoice.",
                      variant: "destructive",
                    });
                    return;
                  }

                  toast({
                    title: "Invoice Will Be Sent",
                    description: `The invoice will be sent to WhatsApp ${whatsappContact} after generation.`,
                  });

                  // First generate invoice, then send WhatsApp when complete
                  generateJobCard().then((invoiceId) => {
                    if (invoiceId) {
                      // Send WhatsApp logic would go here
                      toast({
                        title: "WhatsApp Sent",
                        description: `Invoice has been sent to WhatsApp ${whatsappContact}.`,
                      });
                    }
                  });
                }}
                disabled={invoiceGenerationLoading || !whatsappContact}
              >
                <MessageCircle className="h-4 w-4" /> Send via WhatsApp
              </Button> */}

              <Button
                type="button"
                variant="outline"
                className="flex items-center gap-1 bg-green-50 text-green-700 hover:bg-green-100"
                onClick={printJobCard}
              >
                <FileDown className="h-4 w-4" /> Download Quotation
              </Button>
            </div>

            {/* <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowInvoicePreview(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={generateJobCard}
                disabled={invoiceGenerationLoading}
              >
                {invoiceGenerationLoading
                  ? "Generating..."
                  : "Generate Invoice"}
              </Button>
            </div> */}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default JobCardDetails;
