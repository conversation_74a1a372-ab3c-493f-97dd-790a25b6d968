import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Switch, Route, useLocation } from "wouter";
import { Toaster } from "@/components/ui/toaster";
import AuthPage from "@/pages/auth-page";
import { useState, useEffect } from "react";
import { AuthProvider, useAuth } from "@/hooks/use-auth";
import Sidebar from "@/components/layout/Sidebar";
import Header from "@/components/layout/Header";
import Dashboard from "@/pages/Dashboard";
import GatePass from "@/pages/GatePass";
import JobCard from "@/pages/JobCard";
import CreateJobCard from "@/pages/CreateJobCard";
import Quatations from "@/pages/Quatations";
import JobCardDetails from "@/pages/JobCardDetails";
import NewQuotation from "@/pages/NewQuotation";

import JobCardFromQuotation from "@/pages/JobCardFromQuotation";
// import JobCardFromQuotation from "@/pages/JobCardFromQuotation";
import QuotationRevisit from "@/pages/QuotationRevisit";
import ViewJobCard from "@/pages/ViewJobCard";
import ServiceTracking from "@/pages/ServiceTracking";
import QualityCheck from "@/pages/QualityCheck";
import Billing from "@/pages/Billing";
import Invoice from "@/pages/Invoice";
import VehicleExit from "@/pages/VehicleExit";
import NotFound from "@/pages/not-found";

function MainLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, isLoading } = useAuth();
  const [_, navigate] = useLocation();
  
  // Check authentication
  useEffect(() => {
    if (!isLoading && !user) {
      navigate("/auth");
    }
  }, [isLoading, user, navigate]);
  
  // Show loading while checking auth status
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="h-8 w-8 animate-spin text-primary">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
          </svg>
        </div>
      </div>
    );
  }
  
  // If not authenticated, don't render anything (will redirect in useEffect)
  if (!user) {
    return null;
  }
  
  return (
    <>
      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        <Header setSidebarOpen={setSidebarOpen} />
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <Switch>
            <Route path="/" component={Dashboard} />
            <Route path="/quotation" component={Quatations} />
            <Route path="/job-card" component={JobCard} />
            {/* <Route path="/job-card/new" component={JobCard} /> */}
            <Route path="/job-card/new" component={CreateJobCard} />
            <Route path="/job-card/view/:id" component={ViewJobCard} />
            <Route path="/job-card/:id" component={CreateJobCard} />
            <Route path="/job-card/initiate-from-quotation/:id" component={JobCardFromQuotation} />
            {/* <Route path="/job-card/initiate-Job-Card/:id" component={JobCardFromQuotation} /> */}
            <Route path="/gate-pass/new" component={GatePass} />
            <Route path="/quotation/new" component={NewQuotation} />
            <Route path="/quotation/revisit/:id" component={QuotationRevisit} />
            <Route path="/quotation/:id" component={JobCardDetails} />
            <Route path="/gate-pass" component={GatePass} />
            <Route path="/service-tracking" component={ServiceTracking} />
            <Route path="/quality-check" component={QualityCheck} />
            <Route path="/billing" component={Billing} />
            <Route path="/vehicle-exit" component={VehicleExit} />
            <Route path="/invoices/generate/:jobCardId" component={Invoice} />
            <Route path="/invoices/:id" component={Invoice} />
            <Route path="/invoices" component={Billing} />
            <Route path="/reports" component={Dashboard} />
            <Route component={NotFound} />
          </Switch>
        </main>
      </div>
    </>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <div className="flex h-screen overflow-hidden bg-off-white text-gray-900">
          <Switch>
            <Route path="/auth" component={AuthPage} />
            <Route component={MainLayout} />
          </Switch>
        </div>
        <Toaster />
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
