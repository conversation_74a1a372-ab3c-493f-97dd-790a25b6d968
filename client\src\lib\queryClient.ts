import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    try {
      // Try to parse the response as JSON first
      const contentType = res.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        const errorJson = await res.json();
        console.error("API Error Response (JSON):", errorJson);
        
        // If there are validation errors, format them for better display
        if (errorJson.errors && Array.isArray(errorJson.errors)) {
          const errorMessage = errorJson.errors.map((err: any) => 
            `Field '${err.path.join('.')}': ${err.message || `Expected ${err.expected}, received ${err.received}`}`
          ).join('\n');
          throw new Error(`Validation Error: \n${errorMessage}`);
        }
        
        throw new Error(`${res.status}: ${JSON.stringify(errorJson)}`);
      } else {
        // Fallback to text response
        const text = await res.text();
        console.error("API Error Response (Text):", text);
        throw new Error(`${res.status}: ${text || res.statusText}`);
      }
    } catch (parseError) {
      if (parseError instanceof Error) {
        throw parseError; // Rethrow our custom error with better formatting
      }
      // If parsing failed, fall back to status text
      throw new Error(`${res.status}: ${res.statusText}`);
    }
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  const res = await fetch(url, {
    method,
    headers: data ? { "Content-Type": "application/json" } : {},
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const res = await fetch(queryKey[0] as string, {
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
