import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { CheckCircle, AlertCircle } from "lucide-react";

type QualityCheckFormProps = {
  jobCardId: number;
  onComplete: () => void;
};

// Form validation schema
const qualityCheckSchema = z.object({
  engineCheck: z.boolean().refine((val) => val === true, {
    message: "Engine must pass quality check",
  }),
  brakeCheck: z.boolean().refine((val) => val === true, {
    message: "Brakes must pass quality check",
  }),
  electricalCheck: z.boolean().refine((val) => val === true, {
    message: "Electrical systems must pass quality check",
  }),
  fluidCheck: z.boolean().refine((val) => val === true, {
    message: "Fluid levels must pass quality check",
  }),
  exteriorCheck: z.boolean().refine((val) => val === true, {
    message: "Exterior must pass quality check",
  }),
  interiorCheck: z.boolean().refine((val) => val === true, {
    message: "Interior must pass quality check",
  }),
  testDrive: z.enum(["PASS", "FAIL", "NOT_APPLICABLE"]),
  notes: z.string().optional(),
});

type QualityCheckFormValues = z.infer<typeof qualityCheckSchema>;

const QualityCheckForm = ({ jobCardId, onComplete }: QualityCheckFormProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [photos, setPhotos] = useState<File[]>([]);

  // Initialize form with default values
  const form = useForm<QualityCheckFormValues>({
    resolver: zodResolver(qualityCheckSchema),
    defaultValues: {
      engineCheck: false,
      brakeCheck: false,
      electricalCheck: false,
      fluidCheck: false,
      exteriorCheck: false,
      interiorCheck: false,
      testDrive: "NOT_APPLICABLE",
      notes: "",
    },
  });

  // Update job card mutation
  const updateJobCardMutation = useMutation({
    mutationFn: async (data: {
      qualityCheckPassed: boolean;
      status: string;
      notes?: string;
    }) => {
      const response = await apiRequest(
        "PATCH", 
        `/api/jobcards/${jobCardId}`, 
        data
      );
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/jobcards/${jobCardId}`] });
      queryClient.invalidateQueries({ queryKey: ["/api/jobcards"] });
      toast({
        title: "Quality check complete",
        description: "The quality check has been successfully recorded",
      });
      onComplete();
    },
    onError: (error) => {
      toast({
        title: "Error recording quality check",
        description: "There was an error updating the job card. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setPhotos(Array.from(e.target.files));
    }
  };

  const onSubmit = async (data: QualityCheckFormValues) => {
    try {
      // Check if all required checks passed
      const allChecksPassed =
        data.engineCheck &&
        data.brakeCheck &&
        data.electricalCheck &&
        data.fluidCheck &&
        data.exteriorCheck &&
        data.interiorCheck &&
        data.testDrive !== "FAIL";

      // Update job card with quality check results
      await updateJobCardMutation.mutateAsync({
        qualityCheckPassed: allChecksPassed,
        status: allChecksPassed ? "READY_FOR_PICKUP" : "IN_PROGRESS",
        notes: data.notes,
      });

      // Upload final photos if any (in a real app)
      if (photos.length > 0) {
        // This would be implemented with actual file upload
        console.log(`Would upload ${photos.length} final photos for job card ${jobCardId}`);
      }
    } catch (error) {
      console.error("Error completing quality check:", error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Quality Check</CardTitle>
            <CardDescription>
              Complete all checks to verify the quality of the service
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Mechanical Checks</h3>
              <Separator />

              <FormField
                control={form.control}
                name="engineCheck"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Engine Performance</FormLabel>
                      <p className="text-sm text-gray-500">
                        Check engine performance, idle, and acceleration
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="brakeCheck"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Brake System</FormLabel>
                      <p className="text-sm text-gray-500">
                        Verify brake pads, rotors, and overall braking performance
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="electricalCheck"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Electrical Systems</FormLabel>
                      <p className="text-sm text-gray-500">
                        Check lights, indicators, and all electrical components
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fluidCheck"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Fluid Levels</FormLabel>
                      <p className="text-sm text-gray-500">
                        Verify oil, coolant, brake fluid, and other fluid levels
                      </p>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Visual Inspection</h3>
              <Separator />

              <FormField
                control={form.control}
                name="exteriorCheck"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Exterior Condition</FormLabel>
                      <p className="text-sm text-gray-500">
                        Check body, paint, and overall exterior condition
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="interiorCheck"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Interior Condition</FormLabel>
                      <p className="text-sm text-gray-500">
                        Verify cleanliness and condition of interior components
                      </p>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Test Drive</h3>
              <Separator />

              <FormField
                control={form.control}
                name="testDrive"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Test Drive Result</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="PASS" id="pass" />
                          <Label htmlFor="pass" className="flex items-center">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                            Pass
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="FAIL" id="fail" />
                          <Label htmlFor="fail" className="flex items-center">
                            <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                            Fail
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="NOT_APPLICABLE" id="na" />
                          <Label htmlFor="na">Not Applicable</Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Documentation</h3>
              <Separator />

              <div>
                <Label className="mb-2 block">Final Vehicle Photos</Label>
                <Input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileChange}
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Upload photos of the vehicle's final condition after service
                </p>
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quality Check Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any notes about the quality inspection..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="border-t px-6 py-4 flex justify-between">
            <Button type="button" variant="outline">
              Save Draft
            </Button>
            <Button
              type="submit"
              disabled={updateJobCardMutation.isPending}
            >
              {updateJobCardMutation.isPending
                ? "Completing..."
                : "Complete Quality Check"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
};

export default QualityCheckForm;
