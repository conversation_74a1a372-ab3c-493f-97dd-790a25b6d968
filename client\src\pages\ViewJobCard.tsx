import React from "react";
import { useRoute, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { formatDate, getStatusColor, formatStatusLabel, formatCurrency } from "@/lib/utils";
import {
  CalendarDays,
  Wrench,
  Car,
  User,
  ClipboardList,
  ChevronLeft,
  FileText,
  Package,
  Loader2,
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { fetchAPI } from "@/lib/api";

const ViewJobCard = () => {
  const [, navigate] = useLocation();
  const [match, params] = useRoute("/job-card/view/:id");
  const jobCardId = params?.id;

  // Fetch job card data
  const {
    data: jobCard,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["/api/job-card", jobCardId],
    queryFn: async () => {
      if (!jobCardId) return null;
      const response = await fetchAPI(`/gate-pass/job-card/details/${jobCardId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch job card");
      }
      return await response.json();
    },
    enabled: !!jobCardId,
  });

  // Parse JSON fields safely
  const parseJsonSafely = (jsonString: string | null) => {
    if (!jsonString) return [];
    try {
      return JSON.parse(jsonString);
    } catch {
      return [];
    }
  };

  const partsUsed = parseJsonSafely(jobCard?.partsUsed);
  const servicesUsed = parseJsonSafely(jobCard?.servicesUsed);

  // Calculate totals
  const calculateTotalCost = () => {
    const partsTotal = partsUsed.reduce((sum: number, part: any) =>
      sum + (parseFloat(part.price) || 0), 0);
    const servicesTotal = servicesUsed.reduce((sum: number, service: any) =>
      sum + (parseFloat(service.price) || 0), 0);
    return partsTotal + servicesTotal;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !jobCard) {
    return (
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Job Card Not Found
            </h1>
            <p className="text-gray-600 mb-4">
              The job card you're looking for doesn't exist or has been removed.
            </p>
            <Button onClick={() => navigate("/job-card")}>
              <ChevronLeft className="mr-2 h-4 w-4" /> Back to Job Cards
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Button
              variant="outline"
              onClick={() => navigate("/job-card")}
              className="mr-4"
            >
              <ChevronLeft className="mr-2 h-4 w-4" /> Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Job Card #{jobCard.jobNumber}
              </h1>
              <p className="text-gray-600">
                Created on {formatDate(jobCard.createdAt)}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Badge className={getStatusColor(jobCard.status)}>
              {formatStatusLabel(jobCard.status)}
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Customer Information */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <User className="mr-2 h-5 w-5 text-primary" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="customerName">Customer Name</Label>
                <Input
                  id="customerName"
                  value={jobCard.customerName || ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="ownerName">Owner Name</Label>
                <Input
                  id="ownerName"
                  value={jobCard.ownerName || ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="customerEmail">Email</Label>
                <Input
                  id="customerEmail"
                  value={jobCard.customerEmail || ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="customerPhone">Phone</Label>
                <Input
                  id="customerPhone"
                  value={jobCard.customerPhone || ""}
                  disabled
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Information */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <Car className="mr-2 h-5 w-5 text-primary" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="vehicleRegistrationNumber">Registration Number</Label>
                <Input
                  id="vehicleRegistrationNumber"
                  value={jobCard.vehicleRegistrationNumber || ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="vehicleMake">Make</Label>
                <Input
                  id="vehicleMake"
                  value={jobCard.vehicleMake || ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="vehicleModel">Model</Label>
                <Input
                  id="vehicleModel"
                  value={jobCard.vehicleModel || ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="vehicleColor">Color</Label>
                <Input
                  id="vehicleColor"
                  value={jobCard.vehicleColor || ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="vehicleEngineNumber">Engine Number</Label>
                <Input
                  id="vehicleEngineNumber"
                  value={jobCard.vehicleEngineNumber || ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="vehicleChassisNumber">Chassis Number</Label>
                <Input
                  id="vehicleChassisNumber"
                  value={jobCard.vehicleChassisNumber || ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="mileage">Mileage</Label>
                <Input
                  id="mileage"
                  value={jobCard.mileage || ""}
                  disabled
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>

          {/* Service Information */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <Wrench className="mr-2 h-5 w-5 text-primary" />
                Service Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="serviceType">Service Type</Label>
                <Input
                  id="serviceType"
                  value={jobCard.serviceType || ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="estimatedDeliveryDate">Estimated Delivery Date</Label>
                <Input
                  id="estimatedDeliveryDate"
                  value={jobCard.estimatedDeliveryDate ? formatDate(jobCard.estimatedDeliveryDate) : ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="estimatedCompletionDate">Estimated Completion Date</Label>
                <Input
                  id="estimatedCompletionDate"
                  value={jobCard.estimatedCompletionDate ? formatDate(jobCard.estimatedCompletionDate) : ""}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="totalAmount">Total Amount</Label>
                <Input
                  id="totalAmount"
                  value={jobCard.totalAmount ? formatCurrency(parseFloat(jobCard.totalAmount)) : formatCurrency(calculateTotalCost())}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="tax">Tax</Label>
                <Input
                  id="tax"
                  value={jobCard.tax ? `${jobCard.tax}%` : "0%"}
                  disabled
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Job Description and Notes */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <ClipboardList className="mr-2 h-5 w-5 text-primary" />
                Job Description
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={jobCard.description || jobCard.jobDescription || ""}
                disabled
                className="min-h-[120px]"
                placeholder="No description provided"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <FileText className="mr-2 h-5 w-5 text-primary" />
                Notes & Remarks
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={jobCard.notes || ""}
                  disabled
                  className="mt-1 min-h-[60px]"
                  placeholder="No notes provided"
                />
              </div>
              <div>
                <Label htmlFor="remarks">Remarks</Label>
                <Textarea
                  id="remarks"
                  value={jobCard.remarks || ""}
                  disabled
                  className="mt-1 min-h-[60px]"
                  placeholder="No remarks provided"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Parts and Services */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Package className="mr-2 h-5 w-5 text-primary" />
              Parts & Services
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="parts" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="parts">Parts Used</TabsTrigger>
                <TabsTrigger value="services">Services</TabsTrigger>
              </TabsList>

              <TabsContent value="parts" className="mt-4">
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Part Name</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {partsUsed.length > 0 ? (
                        partsUsed.map((part: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell>{part.itemName || part.name || "N/A"}</TableCell>
                            <TableCell>{part.quantity || 1}</TableCell>
                            <TableCell>{formatCurrency(parseFloat(part.price) || 0)}</TableCell>
                            <TableCell>
                              {formatCurrency((parseFloat(part.price) || 0) * (part.quantity || 1))}
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center text-gray-500">
                            No parts used
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              <TabsContent value="services" className="mt-4">
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Service Name</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {servicesUsed.length > 0 ? (
                        servicesUsed.map((service: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell>{service.itemName || service.name || "N/A"}</TableCell>
                            <TableCell>{service.quantity || 1}</TableCell>
                            <TableCell>{formatCurrency(parseFloat(service.price) || 0)}</TableCell>
                            <TableCell>
                              {formatCurrency((parseFloat(service.price) || 0) * (service.quantity || 1))}
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center text-gray-500">
                            No services provided
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <CalendarDays className="mr-2 h-5 w-5 text-primary" />
                Dates & Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="createdAt">Created Date</Label>
                <Input
                  id="createdAt"
                  value={formatDate(jobCard.createdAt)}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="updatedAt">Last Updated</Label>
                <Input
                  id="updatedAt"
                  value={formatDate(jobCard.updatedAt)}
                  disabled
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <FileText className="mr-2 h-5 w-5 text-primary" />
                Additional Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isInsuranceClaim"
                  checked={jobCard.isInsuranceClaim || false}
                  disabled
                />
                <Label htmlFor="isInsuranceClaim">Insurance Claim</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isPvtClaim"
                  checked={jobCard.isPvtClaim || false}
                  disabled
                />
                <Label htmlFor="isPvtClaim">Private Claim</Label>
              </div>
              {jobCard.quotationNotes && (
                <div>
                  <Label htmlFor="quotationNotes">Quotation Notes</Label>
                  <Textarea
                    id="quotationNotes"
                    value={jobCard.quotationNotes}
                    disabled
                    className="mt-1 min-h-[60px]"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ViewJobCard;