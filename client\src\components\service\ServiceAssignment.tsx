import { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { formatDate } from "@/lib/utils";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

type ServiceAssignmentProps = {
  jobCardId: number;
};

const StatusBadgeColors: Record<string, string> = {
  PENDING: "bg-gray-200 hover:bg-gray-300 text-gray-800",
  IN_PROGRESS: "bg-blue-100 hover:bg-blue-200 text-blue-800",
  PENDING_APPROVAL: "bg-amber-100 hover:bg-amber-200 text-amber-800",
  PARTS_WAITING: "bg-purple-100 hover:bg-purple-200 text-purple-800",
  QUALITY_CHECK: "bg-blue-100 hover:bg-blue-200 text-blue-800",
  READY_FOR_PICKUP: "bg-green-100 hover:bg-green-200 text-green-800",
  COMPLETED: "bg-green-100 hover:bg-green-200 text-green-800",
  CANCELLED: "bg-red-100 hover:bg-red-200 text-red-800",
};

const ServiceAssignment = ({ jobCardId }: ServiceAssignmentProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [progressNotes, setProgressNotes] = useState("");
  const [statusUpdates, setStatusUpdates] = useState<Array<{date: Date; status: string; notes: string}>>([]);

  // Fetch job card details
  const { data: jobCard, isLoading: jobCardLoading } = useQuery({
    queryKey: ["/api/jobcards", jobCardId],
    queryFn: async () => {
      const response = await fetch(`/api/jobcards/${jobCardId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch job card");
      }
      return response.json();
    }
  });

  // Fetch technicians
  const { data: technicians, isLoading: techniciansLoading } = useQuery({
    queryKey: ["/api/technicians"],
    queryFn: async () => {
      const response = await fetch("/api/technicians");
      if (!response.ok) {
        throw new Error("Failed to fetch technicians");
      }
      return response.json();
    }
  });

  // Fetch service items
  const { data: serviceItems, isLoading: serviceItemsLoading } = useQuery({
    queryKey: ["/api/jobcards", jobCardId, "services"],
    queryFn: async () => {
      const response = await fetch(`/api/jobcards/${jobCardId}/services`);
      if (!response.ok) {
        return [];
      }
      return response.json();
    }
  });

  // Fetch part items
  const { data: partItems, isLoading: partItemsLoading } = useQuery({
    queryKey: ["/api/jobcards", jobCardId, "parts"],
    queryFn: async () => {
      const response = await fetch(`/api/jobcards/${jobCardId}/parts`);
      if (!response.ok) {
        return [];
      }
      return response.json();
    }
  });

  // Update job card mutation
  const updateJobCardMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("PATCH", `/api/jobcards/${jobCardId}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/jobcards", jobCardId] });
      queryClient.invalidateQueries({ queryKey: ["/api/jobcards"] });
      toast({
        title: "Job card updated",
        description: "The job card has been successfully updated",
      });
    },
    onError: (error) => {
      toast({
        title: "Error updating job card",
        description: "There was an error updating the job card. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Add status updates for demo purposes
  useEffect(() => {
    if (jobCard && statusUpdates.length === 0) {
      const updates = [];
      
      updates.push({
        date: new Date(jobCard.createdAt),
        status: "PENDING",
        notes: "Job card created"
      });
      
      if (jobCard.status !== "PENDING") {
        updates.push({
          date: new Date(new Date(jobCard.createdAt).getTime() + 3600000), // 1 hour later
          status: "IN_PROGRESS",
          notes: "Work started on vehicle"
        });
      }
      
      if (jobCard.status === "QUALITY_CHECK" || jobCard.status === "READY_FOR_PICKUP" || jobCard.status === "COMPLETED") {
        updates.push({
          date: new Date(new Date(jobCard.createdAt).getTime() + 86400000), // 1 day later
          status: "QUALITY_CHECK",
          notes: "Service completed, performing quality checks"
        });
      }
      
      if (jobCard.status === "READY_FOR_PICKUP" || jobCard.status === "COMPLETED") {
        updates.push({
          date: new Date(new Date(jobCard.createdAt).getTime() + 86400000 + 3600000), // 1 day and 1 hour later
          status: "READY_FOR_PICKUP",
          notes: "Quality check passed, vehicle ready for pickup"
        });
      }
      
      setStatusUpdates(updates);
    }
  }, [jobCard]);

  const handleStatusChange = (status: string) => {
    updateJobCardMutation.mutate({ status });
    setStatusUpdates([
      ...statusUpdates,
      {
        date: new Date(),
        status,
        notes: progressNotes || `Status changed to ${status}`
      }
    ]);
    setProgressNotes("");
  };

  const handleTechnicianChange = (technicianId: string) => {
    updateJobCardMutation.mutate({ technicianId: parseInt(technicianId) });
  };

  const getProgressPercentage = (status: string) => {
    const statusMap: Record<string, number> = {
      PENDING: 10,
      IN_PROGRESS: 40,
      PENDING_APPROVAL: 50,
      PARTS_WAITING: 60,
      QUALITY_CHECK: 80,
      READY_FOR_PICKUP: 95,
      COMPLETED: 100,
      CANCELLED: 0,
    };
    return statusMap[status] || 0;
  };

  const formatStatusName = (status: string) => {
    return status
      .split("_")
      .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
      .join(" ");
  };

  if (jobCardLoading || techniciansLoading || serviceItemsLoading || partItemsLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Service Tracking</CardTitle>
          <CardDescription>Loading service details...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center">
            <Progress value={45} className="w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const progressPercent = getProgressPercentage(jobCard?.status);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Service Progress</CardTitle>
          <CardDescription>
            Current Status: <span className="font-medium">{formatStatusName(jobCard?.status)}</span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Progress value={progressPercent} className="h-2" />
              <div className="flex justify-between mt-1">
                <span className="text-xs text-gray-500">Start</span>
                <span className="text-xs text-gray-500">In Progress</span>
                <span className="text-xs text-gray-500">Complete</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              <div>
                <Label>Assigned Technician</Label>
                <Select
                  defaultValue={jobCard?.technicianId?.toString()}
                  onValueChange={handleTechnicianChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Assign technician" />
                  </SelectTrigger>
                  <SelectContent>
                    {technicians?.map((technician: any) => (
                      <SelectItem key={technician.id} value={technician.id.toString()}>
                        {technician.name} ({technician.specialization})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Update Status</Label>
                <Select
                  defaultValue={jobCard?.status}
                  onValueChange={handleStatusChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Change status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                    <SelectItem value="PENDING_APPROVAL">Pending Approval</SelectItem>
                    <SelectItem value="PARTS_WAITING">Parts Waiting</SelectItem>
                    <SelectItem value="QUALITY_CHECK">Quality Check</SelectItem>
                    <SelectItem value="READY_FOR_PICKUP">Ready for Pickup</SelectItem>
                    <SelectItem value="COMPLETED">Completed</SelectItem>
                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mt-4">
              <Label>Progress Notes</Label>
              <Textarea
                placeholder="Add notes about progress or status changes..."
                value={progressNotes}
                onChange={(e) => setProgressNotes(e.target.value)}
                className="mt-1"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="details">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="details">Service Details</TabsTrigger>
          <TabsTrigger value="parts">Parts Status</TabsTrigger>
          <TabsTrigger value="history">Status History</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Service Items</CardTitle>
              <CardDescription>
                Services to be performed on this job
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Service</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {serviceItems?.length ? (
                    serviceItems.map((item: any) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">
                          {/* In a real app, we'd fetch and display the service name */}
                          Service #{item.serviceId}
                        </TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>${item.price.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={StatusBadgeColors[item.status]}>
                            {formatStatusName(item.status)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                        No service items added to this job
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="parts">
          <Card>
            <CardHeader>
              <CardTitle>Parts Status</CardTitle>
              <CardDescription>
                Parts required for this service and their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Part</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {partItems?.length ? (
                    partItems.map((item: any) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">
                          {/* In a real app, we'd fetch and display the part name */}
                          Part #{item.partId}
                        </TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>${item.price.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={StatusBadgeColors[item.status]}>
                            {formatStatusName(item.status)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                        No parts added to this job
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Status History</CardTitle>
              <CardDescription>
                Timeline of status changes for this job
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative border-l-2 border-gray-200 pl-8 space-y-6 py-2">
                {statusUpdates.map((update, index) => (
                  <div key={index} className="relative">
                    <div className="absolute -left-10 mt-1.5 h-4 w-4 rounded-full border-2 border-white bg-primary"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{formatStatusName(update.status)}</p>
                      <p className="text-xs text-gray-500">{formatDate(update.date)}</p>
                      <p className="mt-1 text-sm text-gray-600">{update.notes}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Customer Notifications</CardTitle>
          <CardDescription>
            Keep the customer informed about service progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label>Send Status Update</Label>
              <Textarea
                placeholder="Write a message to send to the customer..."
                className="mt-1"
              />
            </div>
            <div className="flex space-x-2">
              <Button variant="outline">Send Email</Button>
              <Button variant="outline">Send SMS</Button>
            </div>
          </div>
        </CardContent>
        <CardFooter className="border-t px-6 py-4">
          <p className="text-sm text-gray-500">
            Last notification sent: {jobCard?.updatedAt ? formatDate(jobCard.updatedAt) : "None"}
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ServiceAssignment;
