import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import QualityCheckForm from "@/components/quality/QualityCheckForm";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SearchIcon, ClipboardCheck } from "lucide-react";
import { formatDate, getStatusColor, formatStatusLabel } from "@/lib/utils";

const QualityCheck = () => {
  const [selectedJobId, setSelectedJobId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [formSubmitted, setFormSubmitted] = useState(false);

  // Fetch all job cards
  const { data: jobCards, isLoading } = useQuery({
    queryKey: ["/api/jobcards"],
  });

  // Filter job cards by status and search query
  const filteredJobCards = jobCards
    ? jobCards.filter((job: any) => {
        const matchesSearch =
          job.jobNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
          job.description.toLowerCase().includes(searchQuery.toLowerCase());
        
        // Jobs ready for quality check
        const isCheckable = job.status === "IN_PROGRESS";
        
        return matchesSearch && isCheckable;
      })
    : [];

  const handleFormComplete = () => {
    setFormSubmitted(true);
  };

  const resetForm = () => {
    setSelectedJobId(null);
    setFormSubmitted(false);
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Quality Check</h1>
        <p className="mt-1 text-gray-600">
          Perform quality checks on completed service jobs
        </p>

        <div className="mt-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div className="relative w-full md:w-96">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search job number or description..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Jobs List Panel */}
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle>Jobs Ready for Quality Check</CardTitle>
                <CardDescription>
                  {filteredJobCards.length} jobs awaiting quality check
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <p className="text-center py-4 text-gray-500">Loading jobs...</p>
                ) : filteredJobCards.length === 0 ? (
                  <p className="text-center py-4 text-gray-500">No jobs ready for quality check</p>
                ) : (
                  <ul className="space-y-2">
                    {filteredJobCards.map((job: any) => (
                      <li key={job.id}>
                        <Button
                          variant="outline"
                          className={`w-full justify-start text-left h-auto py-4 ${
                            selectedJobId === job.id ? "border-primary" : ""
                          }`}
                          onClick={() => setSelectedJobId(job.id)}
                          disabled={formSubmitted}
                        >
                          <div className="flex flex-col items-start">
                            <div className="flex items-center">
                              <ClipboardCheck className="h-4 w-4 mr-2 text-gray-500" />
                              <span className="font-medium">{job.jobNumber}</span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1 line-clamp-1">
                              {job.description}
                            </p>
                            <div className="flex justify-between w-full mt-2">
                              <div className={`px-2 py-1 text-xs rounded-full ${getStatusColor(job.status)}`}>
                                {formatStatusLabel(job.status)}
                              </div>
                              <span className="text-xs text-gray-500">
                                {formatDate(job.updatedAt)}
                              </span>
                            </div>
                          </div>
                        </Button>
                      </li>
                    ))}
                  </ul>
                )}
              </CardContent>
            </Card>

            {/* Quality Check Form Panel */}
            <div className="lg:col-span-2">
              {selectedJobId ? (
                formSubmitted ? (
                  <Card>
                    <CardHeader>
                      <CardTitle>Quality Check Complete</CardTitle>
                      <CardDescription>
                        Thank you for completing the quality check
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex flex-col items-center justify-center py-12">
                      <div className="bg-green-100 text-green-800 rounded-full p-4 mb-4">
                        <ClipboardCheck className="h-12 w-12" />
                      </div>
                      <h3 className="text-xl font-medium mb-2">Quality Check Submitted</h3>
                      <p className="text-gray-500 text-center mb-6">
                        The quality check has been successfully recorded.
                      </p>
                      <Button onClick={resetForm}>Check Another Vehicle</Button>
                    </CardContent>
                  </Card>
                ) : (
                  <QualityCheckForm 
                    jobCardId={selectedJobId} 
                    onComplete={handleFormComplete} 
                  />
                )
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>Quality Check</CardTitle>
                    <CardDescription>
                      Select a job from the list to perform a quality check
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <ClipboardCheck className="h-16 w-16 text-gray-300 mb-4" />
                    <p className="text-gray-500 text-center">
                      No job selected. Please select a job from the list to perform a quality check.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QualityCheck;
