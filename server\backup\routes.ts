import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";
import { 
  insertCustomerSchema, insertVehicleSchema, insertJobCardSchema, 
  insertJobServiceItemSchema, insertJobPartItemSchema, insertInvoiceSchema,
  insertActivityLogSchema, JobStatusEnum
} from "@shared/schema";
import multer from "multer";
import path from "path";
import fs from "fs";
import { sendEmail, generateInvoiceHtml, generateWhatsAppMessage } from "./services/emailService";

// Configure multer for file uploads (in memory for this prototype)
const upload = multer({ storage: multer.memoryStorage() });

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), "uploads");
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

export async function registerRoutes(app: Express): Promise<Server> {

// user registration route
  app.post("/api/register", async (req, res) => {

    console.log("Received registration request:", req.body);
    try {
      const { username, password } = req.body;
      if (!username || !password) {
        return res.status(400).json({ message: "Username and password are required" });
      }
      
      // Check if user already exists
      const existingUser = await storage.getUserByUsername(username);
      if (existingUser) {
        return res.status(409).json({ message: "User already exists" });
      }
      
      console.log("Creating new user:", username);
      // Create new user
      // const user = await storage.createUser({ username, password });
      
      // // Log activity
      // await storage.createActivityLog({
      //   action: "CREATE",
      //   entityType: "USER",
      //   entityId: user.id,
      //   details: `New user registered: ${user.username}`
      // });
      
      res.status(201).json(user);
    } catch (error) {
      res.status(500).json({ message: "Error registering user" });
    }
  }
  );





  // API routes
  app.get("/api/status", (_req, res) => {
    res.json({ status: "ok" });
  });

  // Technicians API
  app.get("/api/technicians", async (_req, res) => {
    const technicians = await storage.getTechnicians();
    res.json(technicians);
  });

  app.get("/api/technicians/available", async (_req, res) => {
    const technicians = await storage.getAvailableTechnicians();
    res.json(technicians);
  });

  app.get("/api/technicians/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    const technician = await storage.getTechnician(id);
    
    if (!technician) {
      return res.status(404).json({ message: "Technician not found" });
    }
    
    res.json(technician);
  });

  // Services API
  app.get("/api/services", async (_req, res) => {
    const services = await storage.getServices();
    res.json(services);
  });

  app.get("/api/services/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    const service = await storage.getService(id);
    
    if (!service) {
      return res.status(404).json({ message: "Service not found" });
    }
    
    res.json(service);
  });

  // Parts API
  app.get("/api/parts", async (_req, res) => {
    const parts = await storage.getParts();
    res.json(parts);
  });

  app.get("/api/parts/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    const part = await storage.getPart(id);
    
    if (!part) {
      return res.status(404).json({ message: "Part not found" });
    }
    
    res.json(part);
  });

  // Customers API
  app.post("/api/customers", async (req, res) => {
    try {
      const validatedData = insertCustomerSchema.parse(req.body);
      const customer = await storage.createCustomer(validatedData);
      
      // Log activity
      await storage.createActivityLog({
        action: "CREATE",
        entityType: "CUSTOMER",
        entityId: customer.id,
        details: `New customer created: ${customer.name}`
      });
      
      res.status(201).json(customer);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Error creating customer" });
    }
  });

  app.get("/api/customers", async (_req, res) => {
    const customers = await storage.getCustomers();
    res.json(customers);
  });

  app.get("/api/customers/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    const customer = await storage.getCustomer(id);
    
    if (!customer) {
      return res.status(404).json({ message: "Customer not found" });
    }
    
    res.json(customer);
  });

  // Vehicles API
  app.post("/api/vehicles", async (req, res) => {
    try {
      const validatedData = insertVehicleSchema.parse(req.body);
      
      // Check if the customer exists
      const customer = await storage.getCustomer(validatedData.customerId);
      if (!customer) {
        return res.status(400).json({ message: "Customer not found" });
      }
      
      // Check for duplicate registration or VIN
      const existingRegVehicle = await storage.getVehicleByRegistration(validatedData.registrationNumber);
      if (existingRegVehicle) {
        return res.status(409).json({ message: "Vehicle with this registration number already exists" });
      }
      
      const existingVinVehicle = await storage.getVehicleByVin(validatedData.vin);
      if (existingVinVehicle) {
        return res.status(409).json({ message: "Vehicle with this VIN already exists" });
      }
      
      const vehicle = await storage.createVehicle(validatedData);
      
      // Log activity
      await storage.createActivityLog({
        action: "CREATE",
        entityType: "VEHICLE",
        entityId: vehicle.id,
        details: `New vehicle added: ${vehicle.make} ${vehicle.model} (${vehicle.registrationNumber})`
      });
      
      res.status(201).json(vehicle);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Error creating vehicle" });
    }
  });

  app.post("/api/searchLicensePlate", async (req, res) => {
    console.log("Received license plate search request:", req.body);
    try {
      const { licensePlate } = req.body;
      if (!licensePlate || typeof licensePlate !== "string" || licensePlate.length < 2) {
        return res.status(400).json({ message: "Invalid license plate search term" });
      }

      // Search for vehicle_number in gate_pass table matching the licensePlate (case-insensitive, partial match)
      const results = await storage.db.gate_pass.findMany({
          where: {
            vehicle_number: {
              contains: licensePlate,
              mode: "insensitive"
            }
          }
        });

        // Return the matching records (you can map to only needed fields if required)
        res.json(results);
      } catch (error) {
        console.error("Error searching license plate:", error);
        res.status(500).json({ message: "Error searching license plate" });
      }
    });
  
  app.get("/api/vehicles", async (_req, res) => {
    const vehicles = await storage.getVehicles();
    res.json(vehicles);
  });

  app.get("/api/vehicles/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    const vehicle = await storage.getVehicle(id);
    
    if (!vehicle) {
      return res.status(404).json({ message: "Vehicle not found" });
    }
    
    res.json(vehicle);
  });

  app.get("/api/customers/:customerId/vehicles", async (req, res) => {
    const customerId = parseInt(req.params.customerId);
    const vehicles = await storage.getVehiclesByCustomer(customerId);
    res.json(vehicles);
  });

  // Job Cards API
  app.post("/api/jobcards", async (req, res) => {
    try {
      // Pre-process the request body
      const requestBody = { ...req.body };
      console.log("Original job card request:", requestBody);
      
      // Always create a new customer when registration data is provided
      if (requestBody.customerName) {
        console.log("Creating new customer for job card:", requestBody.customerName);
        // Create a new customer
        const newCustomer = await storage.createCustomer({
          name: requestBody.customerName,
          email: `${requestBody.customerName.replace(/\s+/g, '').toLowerCase()}@example.com`,
          phone: requestBody.customerPhone || "************",
          address: "Customer Address"
        });
        console.log("New customer created:", newCustomer);
        requestBody.customerId = newCustomer.id;
      } else {
        requestBody.customerId = 1; // Default fallback
      }
      
      // Always create a new vehicle when registration data is provided
      if (requestBody.registrationNumber && requestBody.makeModel) {
        console.log("Creating new vehicle for job card:", requestBody.makeModel);
        
        // Parse make and model from makeModel string
        const [make, ...modelParts] = requestBody.makeModel.trim().split(' ');
        const model = modelParts.join(' ');
        
        // Extract year if present in the model string
        let year = 2020;
        const yearMatch = model.match(/\d{4}/);
        if (yearMatch) {
          year = parseInt(yearMatch[0]);
        }
        
        const newVehicle = await storage.createVehicle({
          customerId: requestBody.customerId,
          make: make || "Unknown Make",
          model: model || "Unknown Model",
          year: year,
          color: "Unknown",
          registrationNumber: requestBody.registrationNumber,
          vin: requestBody.chassisNumber || "UNKNOWN-VIN"
        });
        console.log("New vehicle created:", newVehicle);
        requestBody.vehicleId = newVehicle.id;
      } else {
        requestBody.vehicleId = 1; // Default fallback
      }
      
      // Convert estimatedCompletionDate from ISO string to Date if it exists
      if (requestBody.estimatedCompletionDate) {
        requestBody.estimatedCompletionDate = new Date(requestBody.estimatedCompletionDate);
      }
      
      console.log("Processed job card request:", requestBody);
      const validatedData = insertJobCardSchema.parse(requestBody);
      
      // Skip validation for vehicle and customer - they were created earlier in this function
      
      // Validate technician if provided
      if (validatedData.technicianId) {
        const technician = await storage.getTechnician(validatedData.technicianId);
        if (!technician) {
          return res.status(400).json({ message: "Technician not found" });
        }
      }
      
      const jobCard = await storage.createJobCard(validatedData);
      
      // Log activity
      await storage.createActivityLog({
        action: "CREATE",
        entityType: "JOB_CARD",
        entityId: jobCard.id,
        details: `New job card created: ${jobCard.jobNumber}`
      });
      
      res.status(201).json(jobCard);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Error creating job card" });
    }
  });

  app.get("/api/jobcards", async (req, res) => {
    const status = req.query.status as string | undefined;
    
    if (status) {
      try {
        // Validate status
        JobStatusEnum.parse(status);
        const jobCards = await storage.getJobCardsByStatus(status);
        return res.json(jobCards);
      } catch (error) {
        return res.status(400).json({ message: "Invalid status parameter" });
      }
    }
    
    const jobCards = await storage.getJobCards();
    res.json(jobCards);
  });

  app.get("/api/jobcards/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    const jobCard = await storage.getJobCard(id);
    
    if (!jobCard) {
      return res.status(404).json({ message: "Job card not found" });
    }
    
    res.json(jobCard);
  });

  app.patch("/api/jobcards/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const jobCard = await storage.getJobCard(id);
      
      if (!jobCard) {
        return res.status(404).json({ message: "Job card not found" });
      }
      
      // Pre-process the request body
      const requestBody = { ...req.body };
      
      // Convert estimatedCompletionDate from ISO string to Date if it exists
      if (requestBody.estimatedCompletionDate && typeof requestBody.estimatedCompletionDate === 'string') {
        requestBody.estimatedCompletionDate = new Date(requestBody.estimatedCompletionDate);
      }
      
      // Partial validation
      const updateData: Partial<typeof requestBody> = {};
      
      if (requestBody.status) {
        JobStatusEnum.parse(requestBody.status);
        updateData.status = requestBody.status;
      }
      
      if (requestBody.technicianId !== undefined) {
        if (requestBody.technicianId) {
          const technician = await storage.getTechnician(requestBody.technicianId);
          if (!technician) {
            return res.status(400).json({ message: "Technician not found" });
          }
        }
        updateData.technicianId = requestBody.technicianId;
      }
      
      // Copy other allowed fields
      ['description', 'estimatedCompletionDate', 'estimatedCost', 'actualCost', 'notes', 
       'isInsuranceClaim', 'isWarrantyClaim', 'insuranceApproved', 'qualityCheckPassed'].forEach(field => {
        if (requestBody[field] !== undefined) {
          updateData[field] = requestBody[field];
        }
      });
      
      const updatedJobCard = await storage.updateJobCard(id, updateData);
      
      // Log activity
      await storage.createActivityLog({
        action: "UPDATE",
        entityType: "JOB_CARD",
        entityId: jobCard.id,
        details: `Job card ${jobCard.jobNumber} updated`
      });
      
      res.json(updatedJobCard);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Error updating job card" });
    }
  });

  // Job Service Items API
  app.post("/api/jobcards/:jobCardId/services", async (req, res) => {
    try {
      const jobCardId = parseInt(req.params.jobCardId);
      
      // Check if job card exists
      const jobCard = await storage.getJobCard(jobCardId);
      if (!jobCard) {
        return res.status(404).json({ message: "Job card not found" });
      }
      
      const data = {
        ...req.body,
        jobCardId
      };
      
      const validatedData = insertJobServiceItemSchema.parse(data);
      
      // Check if service exists
      const service = await storage.getService(validatedData.serviceId);
      if (!service) {
        return res.status(400).json({ message: "Service not found" });
      }
      
      const jobServiceItem = await storage.createJobServiceItem(validatedData);
      res.status(201).json(jobServiceItem);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Error adding service to job card" });
    }
  });

  app.get("/api/jobcards/:jobCardId/services", async (req, res) => {
    const jobCardId = parseInt(req.params.jobCardId);
    
    // Check if job card exists
    const jobCard = await storage.getJobCard(jobCardId);
    if (!jobCard) {
      return res.status(404).json({ message: "Job card not found" });
    }
    
    const jobServiceItems = await storage.getJobServiceItemsByJobCard(jobCardId);
    res.json(jobServiceItems);
  });

  // Job Part Items API
  app.post("/api/jobcards/:jobCardId/parts", async (req, res) => {
    try {
      const jobCardId = parseInt(req.params.jobCardId);
      
      // Check if job card exists
      const jobCard = await storage.getJobCard(jobCardId);
      if (!jobCard) {
        return res.status(404).json({ message: "Job card not found" });
      }
      
      const data = {
        ...req.body,
        jobCardId
      };
      
      const validatedData = insertJobPartItemSchema.parse(data);
      
      // Check if part exists
      const part = await storage.getPart(validatedData.partId);
      if (!part) {
        return res.status(400).json({ message: "Part not found" });
      }
      
      const jobPartItem = await storage.createJobPartItem(validatedData);
      res.status(201).json(jobPartItem);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Error adding part to job card" });
    }
  });

  app.get("/api/jobcards/:jobCardId/parts", async (req, res) => {
    const jobCardId = parseInt(req.params.jobCardId);
    
    // Check if job card exists
    const jobCard = await storage.getJobCard(jobCardId);
    if (!jobCard) {
      return res.status(404).json({ message: "Job card not found" });
    }
    
    const jobPartItems = await storage.getJobPartItemsByJobCard(jobCardId);
    res.json(jobPartItems);
  });

  // Invoice API
  app.post("/api/invoices", async (req, res) => {
    try {
      // If it's just the jobCardId being passed, generate a full invoice
      if (req.body.jobCardId && Object.keys(req.body).length === 1) {
        const jobCardId = parseInt(req.body.jobCardId);
        
        console.log("Generating invoice for job card ID:", jobCardId);
        
        // Get job card
        const jobCard = await storage.getJobCard(jobCardId);
        if (!jobCard) {
          return res.status(400).json({ message: "Job card not found" });
        }
        
        // Get customer
        const customer = await storage.getCustomer(jobCard.customerId);
        if (!customer) {
          return res.status(400).json({ message: "Customer not found" });
        }
        
        // Get parts and services for total calculation
        const jobServiceItems = await storage.getJobServiceItemsByJobCard(jobCardId);
        const jobPartItems = await storage.getJobPartItemsByJobCard(jobCardId);
        
        // Enhance part items with details
        const enhancedPartItems = [];
        for (const partItem of jobPartItems) {
          const partDetails = await storage.getPart(partItem.partId);
          enhancedPartItems.push({
            ...partItem,
            name: partDetails ? partDetails.name : `Part #${partItem.partId}`,
            partNumber: partDetails ? partDetails.partNumber : 'N/A'
          });
        }
        
        // Enhance service items with details
        const enhancedServiceItems = [];
        for (const serviceItem of jobServiceItems) {
          const serviceDetails = await storage.getService(serviceItem.serviceId);
          enhancedServiceItems.push({
            ...serviceItem,
            name: serviceDetails ? serviceDetails.name : `Service #${serviceItem.serviceId}`,
            description: serviceDetails ? serviceDetails.description : 'N/A'
          });
        }
        
        // Calculate totals
        const subtotal = [
          ...enhancedServiceItems.map((item: any) => item.price * item.quantity),
          ...enhancedPartItems.map((item: any) => item.price * item.quantity)
        ].reduce((sum, item) => sum + item, 0);
        
        const taxRate = 0.05; // 5% VAT
        const taxAmount = subtotal * taxRate;
        const total = subtotal + taxAmount;
        
        // Generate unique invoice number (YYYY-MM-nnn format)
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const invoiceCount = (await storage.getInvoices()).length + 1;
        const invoiceNumber = `INV-${year}${month}-${String(invoiceCount).padStart(3, '0')}`;
        
        // Generate invoice data
        const invoiceData = {
          customerId: jobCard.customerId,
          jobCardId: jobCard.id,
          invoiceNumber,
          subtotal,
          tax: taxAmount,
          total,
          status: "PENDING",
          createdAt: new Date(),
          isInsuranceClaim: jobCard.isInsuranceClaim || false,
          isWarrantyClaim: jobCard.isWarrantyClaim || false
        };
        
        // Create invoice
        const invoice = await storage.createInvoice(invoiceData);
        
        // Update job card's invoiceGenerated status (this is ignored due to schema, just for reference)
        await storage.updateJobCard(jobCard.id, { status: "INVOICED" });
        
        // Log activity
        await storage.createActivityLog({
          action: "CREATE",
          entityType: "INVOICE",
          entityId: invoice.id,
          details: `Invoice ${invoice.invoiceNumber} generated for job ${jobCard.jobNumber}`
        });
        
        // Return the complete invoice data structure including related entities
        res.status(201).json({
          invoice,
          jobCard,
          customer,
          vehicle: await storage.getVehicle(jobCard.vehicleId),
          parts: enhancedPartItems,
          services: enhancedServiceItems
        });
      } else {
        // Original validation path for full invoice data
        const validatedData = insertInvoiceSchema.parse(req.body);
        
        // Check if job card exists
        const jobCard = await storage.getJobCard(validatedData.jobCardId);
        if (!jobCard) {
          return res.status(400).json({ message: "Job card not found" });
        }
        
        // Check if customer exists
        const customer = await storage.getCustomer(validatedData.customerId);
        if (!customer) {
          return res.status(400).json({ message: "Customer not found" });
        }
        
        const invoice = await storage.createInvoice(validatedData);
        
        // Update job card's status
        await storage.updateJobCard(jobCard.id, { status: "INVOICED" });
        
        // Log activity
        await storage.createActivityLog({
          action: "CREATE",
          entityType: "INVOICE",
          entityId: invoice.id,
          details: `Invoice ${invoice.invoiceNumber} generated for job ${jobCard.jobNumber}`
        });
        
        res.status(201).json(invoice);
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      console.error("Error creating invoice:", error);
      res.status(500).json({ message: "Error creating invoice" });
    }
  });

  app.get("/api/invoices", async (_req, res) => {
    const invoices = await storage.getInvoices();
    res.json(invoices);
  });

  app.get("/api/invoices/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    const invoice = await storage.getInvoice(id);
    
    if (!invoice) {
      return res.status(404).json({ message: "Invoice not found" });
    }
    
    // Get the associated job card
    const jobCard = await storage.getJobCard(invoice.jobCardId);
    if (!jobCard) {
      return res.status(404).json({ message: "Associated job card not found" });
    }
    
    // Get customer and vehicle data
    const customer = await storage.getCustomer(invoice.customerId);
    const vehicle = await storage.getVehicle(jobCard.vehicleId);
    
    // Get the job card parts and services
    const jobPartItems = await storage.getJobPartItemsByJobCard(invoice.jobCardId);
    const jobServiceItems = await storage.getJobServiceItemsByJobCard(invoice.jobCardId);
    
    // Enhance part items with details
    const enhancedPartItems = [];
    for (const partItem of jobPartItems) {
      const partDetails = await storage.getPart(partItem.partId);
      enhancedPartItems.push({
        ...partItem,
        name: partDetails ? partDetails.name : `Part #${partItem.partId}`,
        partNumber: partDetails ? partDetails.partNumber : 'N/A'
      });
    }
    
    // Enhance service items with details
    const enhancedServiceItems = [];
    for (const serviceItem of jobServiceItems) {
      const serviceDetails = await storage.getService(serviceItem.serviceId);
      enhancedServiceItems.push({
        ...serviceItem,
        name: serviceDetails ? serviceDetails.name : `Service #${serviceItem.serviceId}`,
        description: serviceDetails ? serviceDetails.description : 'N/A'
      });
    }
    
    // Return all the data needed for the invoice
    res.json({
      invoice,
      jobCard,
      customer,
      vehicle,
      parts: enhancedPartItems,
      services: enhancedServiceItems
    });
  });

  app.patch("/api/invoices/:id/pay", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const invoice = await storage.getInvoice(id);
      
      if (!invoice) {
        return res.status(404).json({ message: "Invoice not found" });
      }
      
      // Validate payment method
      const paymentMethod = req.body.paymentMethod;
      if (!paymentMethod) {
        return res.status(400).json({ message: "Payment method is required" });
      }
      
      const updatedInvoice = await storage.updateInvoice(id, {
        status: "PAID",
        paymentMethod,
        paidAt: new Date()
      });
      
      // Update job card payment status
      await storage.updateJobCard(invoice.jobCardId, { paymentReceived: true });
      
      // Log activity
      await storage.createActivityLog({
        action: "UPDATE",
        entityType: "INVOICE",
        entityId: invoice.id,
        details: `Invoice ${invoice.invoiceNumber} marked as paid via ${paymentMethod}`
      });
      
      res.json(updatedInvoice);
    } catch (error) {
      res.status(500).json({ message: "Error processing payment" });
    }
  });

  // Send invoice via email
  app.post("/api/invoices/:id/send-email", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const invoice = await storage.getInvoice(id);
      
      if (!invoice) {
        return res.status(404).json({ message: "Invoice not found" });
      }

      // Get required data
      const jobCard = await storage.getJobCard(invoice.jobCardId);
      if (!jobCard) {
        return res.status(404).json({ message: "Associated job card not found" });
      }

      const customer = await storage.getCustomer(invoice.customerId);
      if (!customer) {
        return res.status(404).json({ message: "Customer not found" });
      }

      const vehicle = await storage.getVehicle(jobCard.vehicleId);
      const jobServiceItems = await storage.getJobServiceItemsByJobCard(jobCard.id);
      const jobPartItems = await storage.getJobPartItemsByJobCard(jobCard.id);

      // Validate email
      const email = req.body.email || customer.email;
      if (!email) {
        return res.status(400).json({ message: "Email address is required" });
      }

      // Generate invoice HTML
      const invoiceHtml = generateInvoiceHtml(
        invoice, 
        jobCard, 
        customer, 
        vehicle, 
        jobServiceItems, 
        jobPartItems
      );

      // Send email
      const emailSent = await sendEmail({
        to: email,
        from: "<EMAIL>",
        subject: `Invoice #${invoice.invoiceNumber} from MRS Automaintenance LLP`,
        html: invoiceHtml,
      });

      if (!emailSent) {
        // This will happen if SendGrid API key is not configured
        if (!process.env.SENDGRID_API_KEY) {
          return res.status(500).json({ 
            message: "SendGrid API key not configured. Please set SENDGRID_API_KEY environment variable." 
          });
        }
        return res.status(500).json({ message: "Failed to send email" });
      }

      // Log activity
      await storage.createActivityLog({
        action: "EMAIL",
        entityType: "INVOICE",
        entityId: invoice.id,
        details: `Invoice ${invoice.invoiceNumber} sent via email to ${email}`
      });

      res.json({ success: true, message: "Invoice sent successfully via email" });
    } catch (error) {
      console.error("Error sending invoice email:", error);
      res.status(500).json({ message: "Error sending invoice via email", error: String(error) });
    }
  });

  // Send invoice via WhatsApp
  app.post("/api/invoices/:id/send-whatsapp", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const invoice = await storage.getInvoice(id);
      
      if (!invoice) {
        return res.status(404).json({ message: "Invoice not found" });
      }

      // Get required data
      const jobCard = await storage.getJobCard(invoice.jobCardId);
      if (!jobCard) {
        return res.status(404).json({ message: "Associated job card not found" });
      }

      const customer = await storage.getCustomer(invoice.customerId);
      if (!customer) {
        return res.status(404).json({ message: "Customer not found" });
      }

      const vehicle = await storage.getVehicle(jobCard.vehicleId);
      const jobServiceItems = await storage.getJobServiceItemsByJobCard(jobCard.id);
      const jobPartItems = await storage.getJobPartItemsByJobCard(jobCard.id);

      // Validate phone number
      const phoneNumber = req.body.phoneNumber || customer.phone;
      if (!phoneNumber) {
        return res.status(400).json({ message: "Phone number is required" });
      }

      // Generate WhatsApp message
      const whatsappMessage = generateWhatsAppMessage(
        invoice, 
        jobCard, 
        customer, 
        vehicle, 
        jobServiceItems, 
        jobPartItems
      );

      // For now, we'll simulate WhatsApp sending by just logging the message
      // In a production app, you would integrate with the WhatsApp Business API or a service like Twilio
      console.log("Simulating WhatsApp message sending to:", phoneNumber);
      console.log("Message content:", whatsappMessage);

      // Log activity
      await storage.createActivityLog({
        action: "WHATSAPP",
        entityType: "INVOICE",
        entityId: invoice.id,
        details: `Invoice ${invoice.invoiceNumber} sent via WhatsApp to ${phoneNumber}`
      });

      res.json({ success: true, message: "Invoice sent successfully via WhatsApp (simulated)" });
    } catch (error) {
      console.error("Error sending invoice via WhatsApp:", error);
      res.status(500).json({ message: "Error sending invoice via WhatsApp", error: String(error) });
    }
  });

  // Activity Logs API
  app.get("/api/activity", async (_req, res) => {
    const activityLogs = await storage.getActivityLogs();
    res.json(activityLogs);
  });

  // Handle photo uploads for job cards
  app.post("/api/jobcards/:id/photos", upload.array("photos", 10), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const jobCard = await storage.getJobCard(id);
      
      if (!jobCard) {
        return res.status(404).json({ message: "Job card not found" });
      }
      
      if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
        return res.status(400).json({ message: "No photos uploaded" });
      }
      
      const type = req.body.type || "initial";
      const photos: string[] = [];
      
      // In a real app, we'd save these photos to a proper storage service
      // For simplicity, we're just recording the filenames for now
      (req.files as Express.Multer.File[]).forEach((file, index) => {
        const filename = `job_${id}_${type}_${Date.now()}_${index}.jpg`;
        
        // In a real implementation, save the file to disk/cloud storage
        // fs.writeFileSync(path.join(uploadsDir, filename), file.buffer);
        
        photos.push(filename);
      });
      
      // Update job card with new photos
      const updateData: any = {};
      if (type === "initial") {
        updateData.initialPhotos = [...(jobCard.initialPhotos || []), ...photos];
      } else if (type === "final") {
        updateData.finalPhotos = [...(jobCard.finalPhotos || []), ...photos];
      }
      
      const updatedJobCard = await storage.updateJobCard(id, updateData);
      
      // Log activity
      await storage.createActivityLog({
        action: "UPDATE",
        entityType: "JOB_CARD",
        entityId: jobCard.id,
        details: `${photos.length} ${type} photos added to job card ${jobCard.jobNumber}`
      });
      
      res.json({ success: true, jobCard: updatedJobCard });
    } catch (error) {
      res.status(500).json({ message: "Error uploading photos" });
    }
  });

  // Create the HTTP server
  const httpServer = createServer(app);
  
  return httpServer;
}
