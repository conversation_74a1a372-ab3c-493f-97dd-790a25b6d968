import { 
  users, type User, type InsertUser,
  customers, type Customer, type InsertCustomer,
  vehicles, type Vehicle, type InsertVehicle,
  technicians, type Technician, type InsertTechnician,
  services, type Service, type InsertService,
  parts, type Part, type InsertPart,
  jobCards, type JobCard, type InsertJobCard,
  jobServiceItems, type JobServiceItem, type InsertJobServiceItem,
  jobPartItems, type JobPartItem, type InsertJobPartItem,
  invoices, type Invoice, type InsertInvoice,
  activityLogs, type ActivityLog, type InsertActivityLog
} from "@shared/schema";

export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Customer operations
  getCustomer(id: number): Promise<Customer | undefined>;
  getCustomers(): Promise<Customer[]>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;
  updateCustomer(id: number, customer: Partial<InsertCustomer>): Promise<Customer | undefined>;
  
  // Vehicle operations
  getVehicle(id: number): Promise<Vehicle | undefined>;
  getVehicles(): Promise<Vehicle[]>;
  getVehiclesByCustomer(customerId: number): Promise<Vehicle[]>;
  getVehicleByRegistration(registrationNumber: string): Promise<Vehicle | undefined>;
  getVehicleByVin(vin: string): Promise<Vehicle | undefined>;
  createVehicle(vehicle: InsertVehicle): Promise<Vehicle>;
  updateVehicle(id: number, vehicle: Partial<InsertVehicle>): Promise<Vehicle | undefined>;
  
  // Technician operations
  getTechnician(id: number): Promise<Technician | undefined>;
  getTechnicians(): Promise<Technician[]>;
  getAvailableTechnicians(): Promise<Technician[]>;
  createTechnician(technician: InsertTechnician): Promise<Technician>;
  updateTechnician(id: number, technician: Partial<InsertTechnician>): Promise<Technician | undefined>;
  
  // Service operations
  getService(id: number): Promise<Service | undefined>;
  getServices(): Promise<Service[]>;
  createService(service: InsertService): Promise<Service>;
  updateService(id: number, service: Partial<InsertService>): Promise<Service | undefined>;
  
  // Parts operations
  getPart(id: number): Promise<Part | undefined>;
  getParts(): Promise<Part[]>;
  getPartByPartNumber(partNumber: string): Promise<Part | undefined>;
  createPart(part: InsertPart): Promise<Part>;
  updatePart(id: number, part: Partial<InsertPart>): Promise<Part | undefined>;
  
  // Job Cards operations
  getJobCard(id: number): Promise<JobCard | undefined>;
  getJobCards(): Promise<JobCard[]>;
  getJobCardsByStatus(status: string): Promise<JobCard[]>;
  getJobCardsByCustomer(customerId: number): Promise<JobCard[]>;
  getJobCardsByVehicle(vehicleId: number): Promise<JobCard[]>;
  createJobCard(jobCard: InsertJobCard): Promise<JobCard>;
  updateJobCard(id: number, jobCard: Partial<InsertJobCard>): Promise<JobCard | undefined>;
  
  // Job Service Items operations
  getJobServiceItem(id: number): Promise<JobServiceItem | undefined>;
  getJobServiceItemsByJobCard(jobCardId: number): Promise<JobServiceItem[]>;
  createJobServiceItem(jobServiceItem: InsertJobServiceItem): Promise<JobServiceItem>;
  updateJobServiceItem(id: number, jobServiceItem: Partial<InsertJobServiceItem>): Promise<JobServiceItem | undefined>;
  
  // Job Part Items operations
  getJobPartItem(id: number): Promise<JobPartItem | undefined>;
  getJobPartItemsByJobCard(jobCardId: number): Promise<JobPartItem[]>;
  createJobPartItem(jobPartItem: InsertJobPartItem): Promise<JobPartItem>;
  updateJobPartItem(id: number, jobPartItem: Partial<InsertJobPartItem>): Promise<JobPartItem | undefined>;
  
  // Invoice operations
  getInvoice(id: number): Promise<Invoice | undefined>;
  getInvoices(): Promise<Invoice[]>;
  getInvoicesByCustomer(customerId: number): Promise<Invoice[]>;
  getInvoicesByJobCard(jobCardId: number): Promise<Invoice[]>;
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  updateInvoice(id: number, invoice: Partial<InsertInvoice>): Promise<Invoice | undefined>;
  
  // Activity Log operations
  getActivityLog(id: number): Promise<ActivityLog | undefined>;
  getActivityLogs(): Promise<ActivityLog[]>;
  getActivityLogsByEntity(entityType: string, entityId: number): Promise<ActivityLog[]>;
  createActivityLog(activityLog: InsertActivityLog): Promise<ActivityLog>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private customers: Map<number, Customer>;
  private vehicles: Map<number, Vehicle>;
  private technicians: Map<number, Technician>;
  private services: Map<number, Service>;
  private parts: Map<number, Part>;
  private jobCards: Map<number, JobCard>;
  private jobServiceItems: Map<number, JobServiceItem>;
  private jobPartItems: Map<number, JobPartItem>;
  private invoices: Map<number, Invoice>;
  private activityLogs: Map<number, ActivityLog>;
  
  currentUserId: number;
  currentCustomerId: number;
  currentVehicleId: number;
  currentTechnicianId: number;
  currentServiceId: number;
  currentPartId: number;
  currentJobCardId: number;
  currentJobServiceItemId: number;
  currentJobPartItemId: number;
  currentInvoiceId: number;
  currentActivityLogId: number;

  constructor() {
    this.users = new Map();
    this.customers = new Map();
    this.vehicles = new Map();
    this.technicians = new Map();
    this.services = new Map();
    this.parts = new Map();
    this.jobCards = new Map();
    this.jobServiceItems = new Map();
    this.jobPartItems = new Map();
    this.invoices = new Map();
    this.activityLogs = new Map();
    
    this.currentUserId = 1;
    this.currentCustomerId = 1;
    this.currentVehicleId = 1;
    this.currentTechnicianId = 1;
    this.currentServiceId = 1;
    this.currentPartId = 1;
    this.currentJobCardId = 1;
    this.currentJobServiceItemId = 1;
    this.currentJobPartItemId = 1;
    this.currentInvoiceId = 1;
    this.currentActivityLogId = 1;
    
    // Initialize with sample data
    this.initializeSampleData();
  }

  private initializeSampleData() {
    // Add some sample customers with Asian names
    const customers = [
      { id: this.currentCustomerId++, name: "Hiroshi Tanaka", email: "<EMAIL>", phone: "050-1234567", address: "Tokyo Central, Tower A, Apt 304", loyaltyPoints: 250 },
      { id: this.currentCustomerId++, name: "Li Wei", email: "<EMAIL>", phone: "055-7654321", address: "Shanghai Road, Eastern Towers", loyaltyPoints: 150 },
      { id: this.currentCustomerId++, name: "Park Min-Jun", email: "<EMAIL>", phone: "052-9876543", address: "Seoul District, Block C", loyaltyPoints: 300 },
      { id: this.currentCustomerId++, name: "Ananya Patel", email: "<EMAIL>", phone: "056-3456789", address: "Mumbai Heights, Villa 45", loyaltyPoints: 100 },
      { id: this.currentCustomerId++, name: "Nguyen Thi Minh", email: "<EMAIL>", phone: "054-8765432", address: "Hanoi Business District, Tower 8", loyaltyPoints: 200 }
    ];
    
    customers.forEach(customer => {
      this.customers.set(customer.id, customer);
    });
    
    // Add some sample vehicles
    const vehicles = [
      { id: this.currentVehicleId++, customerId: 1, make: "Toyota", model: "Land Cruiser", year: 2021, vin: "1HGCM82633A123456", registrationNumber: "DXB 12345", color: "White", insuranceProvider: "Emirates Insurance", insurancePolicy: "EI-12345678", warrantyInfo: "Extended warranty until 2025" },
      { id: this.currentVehicleId++, customerId: 1, make: "BMW", model: "X5", year: 2022, vin: "WBAKJ4C50BC234567", registrationNumber: "DXB 67890", color: "Black", insuranceProvider: "AXA Gulf", insurancePolicy: "AXA-87654321", warrantyInfo: "Manufacturer warranty until 2026" },
      { id: this.currentVehicleId++, customerId: 2, make: "Mercedes", model: "G-Class", year: 2021, vin: "WDCYC3HF7BX345678", registrationNumber: "ABU 54321", color: "Silver", insuranceProvider: "Oman Insurance", insurancePolicy: "OI-23456789", warrantyInfo: "Premium warranty package" },
      { id: this.currentVehicleId++, customerId: 3, make: "Nissan", model: "Patrol", year: 2020, vin: "JN8AZ2NC9B9456789", registrationNumber: "SHJ 98765", color: "White", insuranceProvider: "RSA Insurance", insurancePolicy: "RSA-34567890", warrantyInfo: "Basic warranty until 2024" },
      { id: this.currentVehicleId++, customerId: 4, make: "Lexus", model: "LX570", year: 2022, vin: "JTJHY7AX2H4567890", registrationNumber: "AJM 13579", color: "Black", insuranceProvider: "Sukoon Insurance", insurancePolicy: "SI-45678901", warrantyInfo: "Premium warranty package until 2027" }
    ];
    
    vehicles.forEach(vehicle => {
      this.vehicles.set(vehicle.id, vehicle);
    });
    
    // Add some sample technicians with Indian names
    const technicians = [
      { id: this.currentTechnicianId++, name: "Rajesh Kumar", specialization: "Engine Specialist", email: "<EMAIL>", phone: "555-1234", isAvailable: true },
      { id: this.currentTechnicianId++, name: "Priya Sharma", specialization: "Body Work Specialist", email: "<EMAIL>", phone: "555-5678", isAvailable: true },
      { id: this.currentTechnicianId++, name: "Vikram Singh", specialization: "Transmission Expert", email: "<EMAIL>", phone: "555-9012", isAvailable: true },
      { id: this.currentTechnicianId++, name: "Ananya Patel", specialization: "Service Technician", email: "<EMAIL>", phone: "555-3456", isAvailable: true },
      { id: this.currentTechnicianId++, name: "Arjun Mehta", specialization: "Electrical Specialist", email: "<EMAIL>", phone: "555-7890", isAvailable: true }
    ];
    
    technicians.forEach(tech => {
      this.technicians.set(tech.id, tech);
    });
    
    // Add some sample services
    const services = [
      { id: this.currentServiceId++, name: "Oil Change", description: "Full synthetic oil change", estimatedHours: 1.0, price: 279.99, category: "Maintenance" },
      { id: this.currentServiceId++, name: "Brake Pad Replacement", description: "Replace front and rear brake pads", estimatedHours: 2.0, price: 849.99, category: "Brakes" },
      { id: this.currentServiceId++, name: "Engine Tune-up", description: "Complete engine tune-up service", estimatedHours: 3.0, price: 1099.99, category: "Engine" },
      { id: this.currentServiceId++, name: "Tire Rotation", description: "Rotate and balance all tires", estimatedHours: 1.0, price: 199.99, category: "Tires" },
      { id: this.currentServiceId++, name: "Transmission Service", description: "Transmission fluid flush and filter replacement", estimatedHours: 2.5, price: 699.99, category: "Transmission" }
    ];
    
    services.forEach(service => {
      this.services.set(service.id, service);
    });
    
    // Add some sample parts
    const parts = [
      { id: this.currentPartId++, name: "Oil Filter", partNumber: "OF-12345", description: "Premium oil filter", price: 54.99, quantity: 20, category: "Filters" },
      { id: this.currentPartId++, name: "Brake Pads (Front)", partNumber: "BP-67890", description: "Ceramic brake pads", price: 329.99, quantity: 12, category: "Brakes" },
      { id: this.currentPartId++, name: "Spark Plugs", partNumber: "SP-23456", description: "Platinum spark plugs", price: 29.99, quantity: 40, category: "Ignition" },
      { id: this.currentPartId++, name: "Air Filter", partNumber: "AF-78901", description: "High-flow air filter", price: 89.99, quantity: 15, category: "Filters" },
      { id: this.currentPartId++, name: "Wiper Blades", partNumber: "WB-34567", description: "All-weather wiper blades", price: 69.99, quantity: 30, category: "Exterior" }
    ];
    
    parts.forEach(part => {
      this.parts.set(part.id, part);
    });
    
    // Add a sample user
    const user: User = {
      id: this.currentUserId++,
      username: "tdavis",
      password: "password123", // In a real app, this would be hashed
      name: "Tom Davis",
      role: "Service Advisor",
      email: "<EMAIL>"
    };
    
    this.users.set(user.id, user);
    
    // Add example job cards with completion percentages
    const jobCards = [
      { 
        id: this.currentJobCardId++, 
        jobNumber: "JC-001", 
        customerId: 1, 
        vehicleId: 1, 
        technicianId: 1, 
        status: "IN_PROGRESS", 
        description: "Complete service and oil change", 
        estimatedCompletionDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // tomorrow
        createdAt: new Date(),
        updatedAt: new Date(),
        completionPercentage: 65,
        currentTask: "Engine oil replacement",
        notes: "Customer reported unusual engine noise",
        isWarrantyClaim: false,
        isInsuranceClaim: false,
        invoiceGenerated: false,
        paymentReceived: false
      },
      { 
        id: this.currentJobCardId++, 
        jobNumber: "JC-002", 
        customerId: 2, 
        vehicleId: 3, 
        technicianId: 3, 
        status: "IN_PROGRESS", 
        description: "Transmission repair and cooling system flush", 
        estimatedCompletionDate: new Date(Date.now() + 48 * 60 * 60 * 1000), // two days from now
        createdAt: new Date(),
        updatedAt: new Date(),
        completionPercentage: 30,
        currentTask: "Transmission fluid draining",
        notes: "Vehicle has trouble switching gears",
        isWarrantyClaim: true,
        isInsuranceClaim: false,
        invoiceGenerated: false,
        paymentReceived: false
      }
    ];
    
    jobCards.forEach(job => {
      this.jobCards.set(job.id, job);
    });
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }
  
  // Customer operations
  async getCustomer(id: number): Promise<Customer | undefined> {
    return this.customers.get(id);
  }

  async getCustomers(): Promise<Customer[]> {
    return Array.from(this.customers.values());
  }

  async createCustomer(customer: InsertCustomer): Promise<Customer> {
    const id = this.currentCustomerId++;
    const newCustomer: Customer = { ...customer, id };
    this.customers.set(id, newCustomer);
    return newCustomer;
  }

  async updateCustomer(id: number, customer: Partial<InsertCustomer>): Promise<Customer | undefined> {
    const existingCustomer = this.customers.get(id);
    if (!existingCustomer) return undefined;
    
    const updatedCustomer: Customer = { ...existingCustomer, ...customer };
    this.customers.set(id, updatedCustomer);
    return updatedCustomer;
  }
  
  // Vehicle operations
  async getVehicle(id: number): Promise<Vehicle | undefined> {
    return this.vehicles.get(id);
  }

  async getVehicles(): Promise<Vehicle[]> {
    return Array.from(this.vehicles.values());
  }

  async getVehiclesByCustomer(customerId: number): Promise<Vehicle[]> {
    return Array.from(this.vehicles.values())
      .filter(vehicle => vehicle.customerId === customerId);
  }

  async getVehicleByRegistration(registrationNumber: string): Promise<Vehicle | undefined> {
    return Array.from(this.vehicles.values())
      .find(vehicle => vehicle.registrationNumber === registrationNumber);
  }

  async getVehicleByVin(vin: string): Promise<Vehicle | undefined> {
    return Array.from(this.vehicles.values())
      .find(vehicle => vehicle.vin === vin);
  }

  async createVehicle(vehicle: InsertVehicle): Promise<Vehicle> {
    const id = this.currentVehicleId++;
    const newVehicle: Vehicle = { ...vehicle, id };
    this.vehicles.set(id, newVehicle);
    return newVehicle;
  }

  async updateVehicle(id: number, vehicle: Partial<InsertVehicle>): Promise<Vehicle | undefined> {
    const existingVehicle = this.vehicles.get(id);
    if (!existingVehicle) return undefined;
    
    const updatedVehicle: Vehicle = { ...existingVehicle, ...vehicle };
    this.vehicles.set(id, updatedVehicle);
    return updatedVehicle;
  }
  
  // Technician operations
  async getTechnician(id: number): Promise<Technician | undefined> {
    return this.technicians.get(id);
  }

  async getTechnicians(): Promise<Technician[]> {
    return Array.from(this.technicians.values());
  }

  async getAvailableTechnicians(): Promise<Technician[]> {
    return Array.from(this.technicians.values())
      .filter(technician => technician.isAvailable);
  }

  async createTechnician(technician: InsertTechnician): Promise<Technician> {
    const id = this.currentTechnicianId++;
    const newTechnician: Technician = { ...technician, id };
    this.technicians.set(id, newTechnician);
    return newTechnician;
  }

  async updateTechnician(id: number, technician: Partial<InsertTechnician>): Promise<Technician | undefined> {
    const existingTechnician = this.technicians.get(id);
    if (!existingTechnician) return undefined;
    
    const updatedTechnician: Technician = { ...existingTechnician, ...technician };
    this.technicians.set(id, updatedTechnician);
    return updatedTechnician;
  }
  
  // Service operations
  async getService(id: number): Promise<Service | undefined> {
    return this.services.get(id);
  }

  async getServices(): Promise<Service[]> {
    return Array.from(this.services.values());
  }

  async createService(service: InsertService): Promise<Service> {
    const id = this.currentServiceId++;
    const newService: Service = { ...service, id };
    this.services.set(id, newService);
    return newService;
  }

  async updateService(id: number, service: Partial<InsertService>): Promise<Service | undefined> {
    const existingService = this.services.get(id);
    if (!existingService) return undefined;
    
    const updatedService: Service = { ...existingService, ...service };
    this.services.set(id, updatedService);
    return updatedService;
  }
  
  // Parts operations
  async getPart(id: number): Promise<Part | undefined> {
    return this.parts.get(id);
  }

  async getParts(): Promise<Part[]> {
    return Array.from(this.parts.values());
  }

  async getPartByPartNumber(partNumber: string): Promise<Part | undefined> {
    return Array.from(this.parts.values())
      .find(part => part.partNumber === partNumber);
  }

  async createPart(part: InsertPart): Promise<Part> {
    const id = this.currentPartId++;
    const newPart: Part = { ...part, id };
    this.parts.set(id, newPart);
    return newPart;
  }

  async updatePart(id: number, part: Partial<InsertPart>): Promise<Part | undefined> {
    const existingPart = this.parts.get(id);
    if (!existingPart) return undefined;
    
    const updatedPart: Part = { ...existingPart, ...part };
    this.parts.set(id, updatedPart);
    return updatedPart;
  }
  
  // Job Cards operations
  async getJobCard(id: number): Promise<JobCard | undefined> {
    return this.jobCards.get(id);
  }

  async getJobCards(): Promise<JobCard[]> {
    return Array.from(this.jobCards.values());
  }

  async getJobCardsByStatus(status: string): Promise<JobCard[]> {
    return Array.from(this.jobCards.values())
      .filter(jobCard => jobCard.status === status);
  }

  async getJobCardsByCustomer(customerId: number): Promise<JobCard[]> {
    return Array.from(this.jobCards.values())
      .filter(jobCard => jobCard.customerId === customerId);
  }

  async getJobCardsByVehicle(vehicleId: number): Promise<JobCard[]> {
    return Array.from(this.jobCards.values())
      .filter(jobCard => jobCard.vehicleId === vehicleId);
  }

  async createJobCard(jobCard: InsertJobCard): Promise<JobCard> {
    const id = this.currentJobCardId++;
    const jobNumber = `JB-${new Date().getFullYear()}-${id.toString().padStart(4, '0')}`;
    const createdAt = new Date();
    const updatedAt = new Date();
    
    const newJobCard: JobCard = { 
      ...jobCard, 
      id, 
      jobNumber, 
      createdAt, 
      updatedAt,
      invoiceGenerated: false,
      paymentReceived: false
    };
    
    this.jobCards.set(id, newJobCard);
    return newJobCard;
  }

  async updateJobCard(id: number, jobCard: Partial<InsertJobCard>): Promise<JobCard | undefined> {
    const existingJobCard = this.jobCards.get(id);
    if (!existingJobCard) return undefined;
    
    const updatedJobCard: JobCard = { 
      ...existingJobCard, 
      ...jobCard, 
      updatedAt: new Date() 
    };
    
    this.jobCards.set(id, updatedJobCard);
    return updatedJobCard;
  }
  
  // Job Service Items operations
  async getJobServiceItem(id: number): Promise<JobServiceItem | undefined> {
    return this.jobServiceItems.get(id);
  }

  async getJobServiceItemsByJobCard(jobCardId: number): Promise<JobServiceItem[]> {
    return Array.from(this.jobServiceItems.values())
      .filter(item => item.jobCardId === jobCardId);
  }

  async createJobServiceItem(jobServiceItem: InsertJobServiceItem): Promise<JobServiceItem> {
    const id = this.currentJobServiceItemId++;
    const newJobServiceItem: JobServiceItem = { ...jobServiceItem, id };
    this.jobServiceItems.set(id, newJobServiceItem);
    return newJobServiceItem;
  }

  async updateJobServiceItem(id: number, jobServiceItem: Partial<InsertJobServiceItem>): Promise<JobServiceItem | undefined> {
    const existingJobServiceItem = this.jobServiceItems.get(id);
    if (!existingJobServiceItem) return undefined;
    
    const updatedJobServiceItem: JobServiceItem = { ...existingJobServiceItem, ...jobServiceItem };
    this.jobServiceItems.set(id, updatedJobServiceItem);
    return updatedJobServiceItem;
  }
  
  // Job Part Items operations
  async getJobPartItem(id: number): Promise<JobPartItem | undefined> {
    return this.jobPartItems.get(id);
  }

  async getJobPartItemsByJobCard(jobCardId: number): Promise<JobPartItem[]> {
    return Array.from(this.jobPartItems.values())
      .filter(item => item.jobCardId === jobCardId);
  }

  async createJobPartItem(jobPartItem: InsertJobPartItem): Promise<JobPartItem> {
    const id = this.currentJobPartItemId++;
    const newJobPartItem: JobPartItem = { ...jobPartItem, id };
    this.jobPartItems.set(id, newJobPartItem);
    return newJobPartItem;
  }

  async updateJobPartItem(id: number, jobPartItem: Partial<InsertJobPartItem>): Promise<JobPartItem | undefined> {
    const existingJobPartItem = this.jobPartItems.get(id);
    if (!existingJobPartItem) return undefined;
    
    const updatedJobPartItem: JobPartItem = { ...existingJobPartItem, ...jobPartItem };
    this.jobPartItems.set(id, updatedJobPartItem);
    return updatedJobPartItem;
  }
  
  // Invoice operations
  async getInvoice(id: number): Promise<Invoice | undefined> {
    return this.invoices.get(id);
  }

  async getInvoices(): Promise<Invoice[]> {
    return Array.from(this.invoices.values());
  }

  async getInvoicesByCustomer(customerId: number): Promise<Invoice[]> {
    return Array.from(this.invoices.values())
      .filter(invoice => invoice.customerId === customerId);
  }

  async getInvoicesByJobCard(jobCardId: number): Promise<Invoice[]> {
    return Array.from(this.invoices.values())
      .filter(invoice => invoice.jobCardId === jobCardId);
  }

  async createInvoice(invoice: InsertInvoice): Promise<Invoice> {
    const id = this.currentInvoiceId++;
    const invoiceNumber = `INV-${new Date().getFullYear()}-${id.toString().padStart(4, '0')}`;
    const createdAt = new Date();
    
    const newInvoice: Invoice = { 
      ...invoice, 
      id, 
      invoiceNumber, 
      createdAt,
      paidAt: null
    };
    
    this.invoices.set(id, newInvoice);
    return newInvoice;
  }

  async updateInvoice(id: number, invoice: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    const existingInvoice = this.invoices.get(id);
    if (!existingInvoice) return undefined;
    
    const updatedInvoice: Invoice = { ...existingInvoice, ...invoice };
    this.invoices.set(id, updatedInvoice);
    return updatedInvoice;
  }
  
  // Activity Log operations
  async getActivityLog(id: number): Promise<ActivityLog | undefined> {
    return this.activityLogs.get(id);
  }

  async getActivityLogs(): Promise<ActivityLog[]> {
    return Array.from(this.activityLogs.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  async getActivityLogsByEntity(entityType: string, entityId: number): Promise<ActivityLog[]> {
    return Array.from(this.activityLogs.values())
      .filter(log => log.entityType === entityType && log.entityId === entityId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  async createActivityLog(activityLog: InsertActivityLog): Promise<ActivityLog> {
    const id = this.currentActivityLogId++;
    const timestamp = new Date();
    
    const newActivityLog: ActivityLog = { ...activityLog, id, timestamp };
    this.activityLogs.set(id, newActivityLog);
    return newActivityLog;
  }
}

import { db } from "../db";
import { eq } from "drizzle-orm";

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }
  
  // Customer operations
  async getCustomer(id: number): Promise<Customer | undefined> {
    const [customer] = await db.select().from(customers).where(eq(customers.id, id));
    return customer || undefined;
  }
  
  async getCustomers(): Promise<Customer[]> {
    return db.select().from(customers);
  }
  
  async createCustomer(customer: InsertCustomer): Promise<Customer> {
    const [newCustomer] = await db
      .insert(customers)
      .values(customer)
      .returning();
    return newCustomer;
  }
  
  async updateCustomer(id: number, customer: Partial<InsertCustomer>): Promise<Customer | undefined> {
    const [updatedCustomer] = await db
      .update(customers)
      .set(customer)
      .where(eq(customers.id, id))
      .returning();
    return updatedCustomer || undefined;
  }
  
  // Vehicle operations
  async getVehicle(id: number): Promise<Vehicle | undefined> {
    const [vehicle] = await db.select().from(vehicles).where(eq(vehicles.id, id));
    return vehicle || undefined;
  }
  
  async getVehicles(): Promise<Vehicle[]> {
    return db.select().from(vehicles);
  }
  
  async getVehiclesByCustomer(customerId: number): Promise<Vehicle[]> {
    return db.select().from(vehicles).where(eq(vehicles.customerId, customerId));
  }
  
  async getVehicleByRegistration(registrationNumber: string): Promise<Vehicle | undefined> {
    const [vehicle] = await db.select().from(vehicles).where(eq(vehicles.registrationNumber, registrationNumber));
    return vehicle || undefined;
  }
  
  async getVehicleByVin(vin: string): Promise<Vehicle | undefined> {
    const [vehicle] = await db.select().from(vehicles).where(eq(vehicles.vin, vin));
    return vehicle || undefined;
  }
  
  async createVehicle(vehicle: InsertVehicle): Promise<Vehicle> {
    const [newVehicle] = await db
      .insert(vehicles)
      .values(vehicle)
      .returning();
    return newVehicle;
  }
  
  async updateVehicle(id: number, vehicle: Partial<InsertVehicle>): Promise<Vehicle | undefined> {
    const [updatedVehicle] = await db
      .update(vehicles)
      .set(vehicle)
      .where(eq(vehicles.id, id))
      .returning();
    return updatedVehicle || undefined;
  }
  
  // Technician operations
  async getTechnician(id: number): Promise<Technician | undefined> {
    const [technician] = await db.select().from(technicians).where(eq(technicians.id, id));
    return technician || undefined;
  }
  
  async getTechnicians(): Promise<Technician[]> {
    return db.select().from(technicians);
  }
  
  async getAvailableTechnicians(): Promise<Technician[]> {
    return db.select().from(technicians).where(eq(technicians.isAvailable, true));
  }
  
  async createTechnician(technician: InsertTechnician): Promise<Technician> {
    const [newTechnician] = await db
      .insert(technicians)
      .values(technician)
      .returning();
    return newTechnician;
  }
  
  async updateTechnician(id: number, technician: Partial<InsertTechnician>): Promise<Technician | undefined> {
    const [updatedTechnician] = await db
      .update(technicians)
      .set(technician)
      .where(eq(technicians.id, id))
      .returning();
    return updatedTechnician || undefined;
  }
  
  // Service operations
  async getService(id: number): Promise<Service | undefined> {
    const [service] = await db.select().from(services).where(eq(services.id, id));
    return service || undefined;
  }
  
  async getServices(): Promise<Service[]> {
    return db.select().from(services);
  }
  
  async createService(service: InsertService): Promise<Service> {
    const [newService] = await db
      .insert(services)
      .values(service)
      .returning();
    return newService;
  }
  
  async updateService(id: number, service: Partial<InsertService>): Promise<Service | undefined> {
    const [updatedService] = await db
      .update(services)
      .set(service)
      .where(eq(services.id, id))
      .returning();
    return updatedService || undefined;
  }
  
  // Parts operations
  async getPart(id: number): Promise<Part | undefined> {
    const [part] = await db.select().from(parts).where(eq(parts.id, id));
    return part || undefined;
  }
  
  async getParts(): Promise<Part[]> {
    return db.select().from(parts);
  }
  
  async getPartByPartNumber(partNumber: string): Promise<Part | undefined> {
    const [part] = await db.select().from(parts).where(eq(parts.partNumber, partNumber));
    return part || undefined;
  }
  
  async createPart(part: InsertPart): Promise<Part> {
    const [newPart] = await db
      .insert(parts)
      .values(part)
      .returning();
    return newPart;
  }
  
  async updatePart(id: number, part: Partial<InsertPart>): Promise<Part | undefined> {
    const [updatedPart] = await db
      .update(parts)
      .set(part)
      .where(eq(parts.id, id))
      .returning();
    return updatedPart || undefined;
  }
  
  // Job Cards operations
  async getJobCard(id: number): Promise<JobCard | undefined> {
    const [jobCard] = await db.select().from(jobCards).where(eq(jobCards.id, id));
    return jobCard || undefined;
  }
  
  async getJobCards(): Promise<JobCard[]> {
    return db.select().from(jobCards);
  }
  
  async getJobCardsByStatus(status: string): Promise<JobCard[]> {
    return db.select().from(jobCards).where(eq(jobCards.status, status));
  }
  
  async getJobCardsByCustomer(customerId: number): Promise<JobCard[]> {
    return db.select().from(jobCards).where(eq(jobCards.customerId, customerId));
  }
  
  async getJobCardsByVehicle(vehicleId: number): Promise<JobCard[]> {
    return db.select().from(jobCards).where(eq(jobCards.vehicleId, vehicleId));
  }
  
  async createJobCard(jobCard: InsertJobCard): Promise<JobCard> {
    const [newJobCard] = await db
      .insert(jobCards)
      .values(jobCard)
      .returning();
    return newJobCard;
  }
  
  async updateJobCard(id: number, jobCard: Partial<InsertJobCard>): Promise<JobCard | undefined> {
    const [updatedJobCard] = await db
      .update(jobCards)
      .set(jobCard)
      .where(eq(jobCards.id, id))
      .returning();
    return updatedJobCard || undefined;
  }
  
  // Job Service Items operations
  async getJobServiceItem(id: number): Promise<JobServiceItem | undefined> {
    const [jobServiceItem] = await db.select().from(jobServiceItems).where(eq(jobServiceItems.id, id));
    return jobServiceItem || undefined;
  }
  
  async getJobServiceItemsByJobCard(jobCardId: number): Promise<JobServiceItem[]> {
    return db.select().from(jobServiceItems).where(eq(jobServiceItems.jobCardId, jobCardId));
  }
  
  async createJobServiceItem(jobServiceItem: InsertJobServiceItem): Promise<JobServiceItem> {
    const [newJobServiceItem] = await db
      .insert(jobServiceItems)
      .values(jobServiceItem)
      .returning();
    return newJobServiceItem;
  }
  
  async updateJobServiceItem(id: number, jobServiceItem: Partial<InsertJobServiceItem>): Promise<JobServiceItem | undefined> {
    const [updatedJobServiceItem] = await db
      .update(jobServiceItems)
      .set(jobServiceItem)
      .where(eq(jobServiceItems.id, id))
      .returning();
    return updatedJobServiceItem || undefined;
  }
  
  // Job Part Items operations
  async getJobPartItem(id: number): Promise<JobPartItem | undefined> {
    const [jobPartItem] = await db.select().from(jobPartItems).where(eq(jobPartItems.id, id));
    return jobPartItem || undefined;
  }
  
  async getJobPartItemsByJobCard(jobCardId: number): Promise<JobPartItem[]> {
    return db.select().from(jobPartItems).where(eq(jobPartItems.jobCardId, jobCardId));
  }
  
  async createJobPartItem(jobPartItem: InsertJobPartItem): Promise<JobPartItem> {
    const [newJobPartItem] = await db
      .insert(jobPartItems)
      .values(jobPartItem)
      .returning();
    return newJobPartItem;
  }
  
  async updateJobPartItem(id: number, jobPartItem: Partial<InsertJobPartItem>): Promise<JobPartItem | undefined> {
    const [updatedJobPartItem] = await db
      .update(jobPartItems)
      .set(jobPartItem)
      .where(eq(jobPartItems.id, id))
      .returning();
    return updatedJobPartItem || undefined;
  }
  
  // Invoice operations
  async getInvoice(id: number): Promise<Invoice | undefined> {
    const [invoice] = await db.select().from(invoices).where(eq(invoices.id, id));
    return invoice || undefined;
  }
  
  async getInvoices(): Promise<Invoice[]> {
    return db.select().from(invoices);
  }
  
  async getInvoicesByCustomer(customerId: number): Promise<Invoice[]> {
    return db.select().from(invoices).where(eq(invoices.customerId, customerId));
  }
  
  async getInvoicesByJobCard(jobCardId: number): Promise<Invoice[]> {
    return db.select().from(invoices).where(eq(invoices.jobCardId, jobCardId));
  }
  
  async createInvoice(invoice: InsertInvoice): Promise<Invoice> {
    const [newInvoice] = await db
      .insert(invoices)
      .values(invoice)
      .returning();
    return newInvoice;
  }
  
  async updateInvoice(id: number, invoice: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    const [updatedInvoice] = await db
      .update(invoices)
      .set(invoice)
      .where(eq(invoices.id, id))
      .returning();
    return updatedInvoice || undefined;
  }
  
  // Activity Log operations
  async getActivityLog(id: number): Promise<ActivityLog | undefined> {
    const [activityLog] = await db.select().from(activityLogs).where(eq(activityLogs.id, id));
    return activityLog || undefined;
  }
  
  async getActivityLogs(): Promise<ActivityLog[]> {
    return db.select().from(activityLogs);
  }
  
  async getActivityLogsByEntity(entityType: string, entityId: number): Promise<ActivityLog[]> {
    return db.select().from(activityLogs)
      .where(eq(activityLogs.entityType, entityType))
      .where(eq(activityLogs.entityId, entityId));
  }
  
  async createActivityLog(activityLog: InsertActivityLog): Promise<ActivityLog> {
    const [newActivityLog] = await db
      .insert(activityLogs)
      .values(activityLog)
      .returning();
    return newActivityLog;
  }
}

// Comment this out for now until we have the database fully set up
// export const storage = new DatabaseStorage();

// Keep using the in-memory storage for now
export const storage = new MemStorage();
